/**
 * Enhanced Real-time Service with SignalR
 * Handles document collaboration, notifications, and live updates
 * Integrates with advanced collaboration tools and AI personalization
 */

import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr'
import { performanceMonitor } from '../lib/performance'
import { memoryCache } from '../lib/cache'
import { backendApiClient } from './backend-api-client'

export interface CollaborationEvent {
  type: 'cursor' | 'selection' | 'edit' | 'comment' | 'presence'
  userId: string
  userName: string
  documentId: string
  data: any
  timestamp: number
}

export interface PresenceInfo {
  userId: string
  userName: string
  avatar?: string
  cursor?: { line: number; column: number }
  selection?: { start: { line: number; column: number }; end: { line: number; column: number } }
  lastSeen: number
}

export interface DocumentLock {
  id: string
  documentId: string
  sessionId: string
  userId: string
  userName: string
  type: 'full' | 'section' | 'element'
  scope?: {
    startLine?: number
    endLine?: number
    elementId?: string
  }
  lockedAt: number
  expiresAt: number
  reason?: string
}

export interface CollaborationSession {
  id: string
  documentId: string
  organizationId: string
  projectId?: string
  title: string
  description?: string
  type: 'document_editing' | 'review' | 'brainstorming' | 'meeting'
  status: 'active' | 'paused' | 'completed' | 'cancelled'
  participants: CollaborationParticipant[]
  settings: {
    allowAnonymous: boolean
    requireApproval: boolean
    maxParticipants: number
    lockTimeout: number
    autoSave: boolean
  }
  createdBy: string
  createdAt: string
  updatedAt: string
  expiresAt?: string
}

export interface CollaborationParticipant {
  userId: string
  userName: string
  email: string
  role: 'owner' | 'editor' | 'reviewer' | 'viewer'
  status: 'online' | 'offline' | 'away' | 'busy'
  joinedAt: string
  lastActivity: string
  permissions: {
    canEdit: boolean
    canComment: boolean
    canShare: boolean
    canLock: boolean
  }
  presence: {
    cursor?: { line: number; column: number }
    selection?: { start: { line: number; column: number }; end: { line: number; column: number } }
    viewport?: { top: number; bottom: number }
  }
}

export interface CollaborationInsight {
  id: string
  sessionId: string
  type: 'productivity' | 'engagement' | 'conflict' | 'suggestion'
  title: string
  description: string
  confidence: number
  actionable: boolean
  recommendations: string[]
  generatedAt: string
}

class RealtimeService {
  private connection: HubConnection | null = null
  private isConnected = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private presenceMap = new Map<string, PresenceInfo>()
  private documentLocks = new Map<string, DocumentLock>()
  private collaborationSessions = new Map<string, CollaborationSession>()
  private eventHandlers = new Map<string, Set<Function>>()
  private currentUserId?: string
  private currentUserName?: string

  constructor() {
    this.initializeConnection()
  }

  private initializeConnection(): void {
    if (typeof window === 'undefined') return

    const hubUrl = process.env.NEXT_PUBLIC_SIGNALR_HUB_URL || '/hubs/collaboration'
    
    this.connection = new HubConnectionBuilder()
      .withUrl(hubUrl, {
        accessTokenFactory: () => {
          // Get token from auth store or localStorage
          return localStorage.getItem('auth_token') || ''
        }
      })
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: (retryContext) => {
          if (retryContext.previousRetryCount < 3) {
            return Math.min(1000 * Math.pow(2, retryContext.previousRetryCount), 30000)
          }
          return null // Stop retrying after 3 attempts
        }
      })
      .configureLogging(LogLevel.Information)
      .build()

    this.setupEventHandlers()
  }

  private setupEventHandlers(): void {
    if (!this.connection) return

    // Connection events
    this.connection.onclose((error) => {
      console.warn('SignalR connection closed:', error)
      this.isConnected = false
      this.handleDisconnection()
    })

    this.connection.onreconnecting((error) => {
      console.log('SignalR reconnecting:', error)
      this.isConnected = false
    })

    this.connection.onreconnected((connectionId) => {
      console.log('SignalR reconnected:', connectionId)
      this.isConnected = true
      this.reconnectAttempts = 0
      this.rejoinActiveDocuments()
    })

    // Collaboration events
    this.connection.on('DocumentEdit', (event: CollaborationEvent) => {
      this.handleCollaborationEvent('edit', event)
    })

    this.connection.on('CursorMove', (event: CollaborationEvent) => {
      this.handleCollaborationEvent('cursor', event)
      this.updatePresence(event.userId, { cursor: event.data.cursor })
    })

    this.connection.on('UserJoined', (event: CollaborationEvent) => {
      this.handleCollaborationEvent('presence', event)
      this.addPresence(event.userId, {
        userId: event.userId,
        userName: event.userName,
        lastSeen: Date.now()
      })
    })

    this.connection.on('UserLeft', (event: CollaborationEvent) => {
      this.handleCollaborationEvent('presence', event)
      this.removePresence(event.userId)
    })

    this.connection.on('DocumentLocked', (lock: DocumentLock) => {
      this.documentLocks.set(lock.documentId, lock)
      this.emit('documentLocked', lock)
    })

    this.connection.on('DocumentUnlocked', (documentId: string) => {
      this.documentLocks.delete(documentId)
      this.emit('documentUnlocked', { documentId })
    })

    // Notification events
    this.connection.on('Notification', (notification: any) => {
      this.emit('notification', notification)
    })

    // System events
    this.connection.on('SystemMessage', (message: any) => {
      this.emit('systemMessage', message)
    })
  }

  async connect(): Promise<void> {
    if (!this.connection || this.isConnected) return

    try {
      const startTime = performance.now()
      await this.connection.start()
      const duration = performance.now() - startTime
      
      performanceMonitor.recordMetric('SignalR_Connect', duration)
      
      this.isConnected = true
      this.reconnectAttempts = 0
      
      console.log('SignalR connected successfully')
      this.emit('connected', { connectionId: this.connection.connectionId })
      
    } catch (error) {
      console.error('Failed to connect to SignalR:', error)
      this.handleConnectionError(error)
    }
  }

  async disconnect(): Promise<void> {
    if (!this.connection || !this.isConnected) return

    try {
      await this.connection.stop()
      this.isConnected = false
      console.log('SignalR disconnected')
      this.emit('disconnected', {})
    } catch (error) {
      console.error('Error disconnecting from SignalR:', error)
    }
  }

  async joinDocument(documentId: string): Promise<void> {
    if (!this.isConnected || !this.connection) {
      await this.connect()
    }

    try {
      await this.connection!.invoke('JoinDocument', documentId)
      console.log(`Joined document: ${documentId}`)
      
      // Cache active document
      const activeDocuments = memoryCache.get('activeDocuments') || []
      if (!activeDocuments.includes(documentId)) {
        activeDocuments.push(documentId)
        memoryCache.set('activeDocuments', activeDocuments, 60 * 60 * 1000) // 1 hour
      }
      
    } catch (error) {
      console.error(`Failed to join document ${documentId}:`, error)
      throw error
    }
  }

  async leaveDocument(documentId: string): Promise<void> {
    if (!this.isConnected || !this.connection) return

    try {
      await this.connection.invoke('LeaveDocument', documentId)
      console.log(`Left document: ${documentId}`)
      
      // Remove from active documents
      const activeDocuments = memoryCache.get('activeDocuments') || []
      const updatedDocuments = activeDocuments.filter((id: string) => id !== documentId)
      memoryCache.set('activeDocuments', updatedDocuments, 60 * 60 * 1000)
      
    } catch (error) {
      console.error(`Failed to leave document ${documentId}:`, error)
    }
  }

  async sendEdit(documentId: string, edit: any): Promise<void> {
    if (!this.isConnected || !this.connection) return

    try {
      await this.connection.invoke('SendEdit', documentId, edit)
    } catch (error) {
      console.error('Failed to send edit:', error)
      throw error
    }
  }

  async sendCursorPosition(documentId: string, cursor: { line: number; column: number }): Promise<void> {
    if (!this.isConnected || !this.connection) return

    try {
      await this.connection.invoke('SendCursor', documentId, cursor)
    } catch (error) {
      console.error('Failed to send cursor position:', error)
    }
  }

  async lockDocument(documentId: string): Promise<boolean> {
    if (!this.isConnected || !this.connection) return false

    try {
      const result = await this.connection.invoke('LockDocument', documentId)
      return result
    } catch (error) {
      console.error('Failed to lock document:', error)
      return false
    }
  }

  async unlockDocument(documentId: string): Promise<void> {
    if (!this.isConnected || !this.connection) return

    try {
      await this.connection.invoke('UnlockDocument', documentId)
    } catch (error) {
      console.error('Failed to unlock document:', error)
    }
  }

  // Event handling
  on(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set())
    }
    this.eventHandlers.get(event)!.add(handler)
  }

  off(event: string, handler: Function): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.delete(handler)
    }
  }

  private emit(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`Error in event handler for ${event}:`, error)
        }
      })
    }
  }

  // Presence management
  private addPresence(userId: string, presence: PresenceInfo): void {
    this.presenceMap.set(userId, presence)
    this.emit('presenceUpdated', Array.from(this.presenceMap.values()))
  }

  private updatePresence(userId: string, updates: Partial<PresenceInfo>): void {
    const existing = this.presenceMap.get(userId)
    if (existing) {
      this.presenceMap.set(userId, { ...existing, ...updates, lastSeen: Date.now() })
      this.emit('presenceUpdated', Array.from(this.presenceMap.values()))
    }
  }

  private removePresence(userId: string): void {
    this.presenceMap.delete(userId)
    this.emit('presenceUpdated', Array.from(this.presenceMap.values()))
  }

  getPresence(): PresenceInfo[] {
    return Array.from(this.presenceMap.values())
  }

  getDocumentLock(documentId: string): DocumentLock | undefined {
    return this.documentLocks.get(documentId)
  }

  isDocumentLocked(documentId: string): boolean {
    const lock = this.documentLocks.get(documentId)
    return lock ? lock.expiresAt > Date.now() : false
  }

  private handleCollaborationEvent(type: string, event: CollaborationEvent): void {
    this.emit(type, event)
    this.emit('collaborationEvent', { type, event })
  }

  private handleDisconnection(): void {
    this.presenceMap.clear()
    this.documentLocks.clear()
    this.emit('disconnected', {})
  }

  private handleConnectionError(error: any): void {
    this.reconnectAttempts++
    
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)
        this.connect()
      }, this.reconnectDelay * this.reconnectAttempts)
    } else {
      console.error('Max reconnection attempts reached')
      this.emit('connectionFailed', { error, attempts: this.reconnectAttempts })
    }
  }

  private async rejoinActiveDocuments(): Promise<void> {
    const activeDocuments = memoryCache.get('activeDocuments') || []
    
    for (const documentId of activeDocuments) {
      try {
        await this.joinDocument(documentId)
      } catch (error) {
        console.error(`Failed to rejoin document ${documentId}:`, error)
      }
    }
  }

  // Enhanced Collaboration Methods
  async createCollaborationSession(sessionData: {
    documentId: string
    organizationId: string
    projectId?: string
    title: string
    description?: string
    type?: 'document_editing' | 'review' | 'brainstorming' | 'meeting'
    settings?: any
  }): Promise<CollaborationSession | null> {
    try {
      const session = await backendApiClient.request<CollaborationSession>('/collaboration/sessions', {
        method: 'POST',
        body: JSON.stringify(sessionData)
      })

      this.collaborationSessions.set(session.id, session)

      // Join the SignalR group for this session
      if (this.isConnected && this.connection) {
        await this.connection.invoke('JoinGroup', `collaboration:${session.id}`)
      }

      return session
    } catch (error) {
      console.error('Failed to create collaboration session:', error)
      return null
    }
  }

  async joinCollaborationSession(sessionId: string): Promise<CollaborationSession | null> {
    try {
      const session = await backendApiClient.request<CollaborationSession>(`/collaboration/sessions/${sessionId}/join`, {
        method: 'POST'
      })

      this.collaborationSessions.set(session.id, session)

      // Join the SignalR group for this session
      if (this.isConnected && this.connection) {
        await this.connection.invoke('JoinGroup', `collaboration:${session.id}`)
      }

      this.emit('sessionJoined', session)
      return session
    } catch (error) {
      console.error('Failed to join collaboration session:', error)
      return null
    }
  }

  async lockDocumentAdvanced(
    documentId: string,
    sessionId: string,
    type: 'full' | 'section' | 'element' = 'full',
    scope?: any,
    reason?: string
  ): Promise<DocumentLock | null> {
    try {
      const lock = await backendApiClient.request<DocumentLock>('/collaboration/lock', {
        method: 'POST',
        body: JSON.stringify({
          documentId,
          sessionId,
          type,
          scope,
          reason
        })
      })

      this.documentLocks.set(`${documentId}:${type}`, lock)
      this.emit('documentLocked', lock)
      return lock
    } catch (error) {
      console.error('Failed to lock document:', error)
      return null
    }
  }

  async updatePresenceAdvanced(
    sessionId: string,
    presence: {
      cursor?: { line: number; column: number }
      selection?: { start: { line: number; column: number }; end: { line: number; column: number } }
      viewport?: { top: number; bottom: number }
    }
  ): Promise<void> {
    try {
      await backendApiClient.request<void>('/collaboration/presence', {
        method: 'POST',
        body: JSON.stringify({
          sessionId,
          presence
        })
      })
    } catch (error) {
      console.error('Failed to update presence:', error)
    }
  }

  async getCollaborationInsights(sessionId: string): Promise<CollaborationInsight[]> {
    try {
      const response = await backendApiClient.request<{ insights: CollaborationInsight[] }>(`/collaboration/sessions/${sessionId}/insights`, {
        method: 'GET'
      })

      return response.insights
    } catch (error) {
      console.error('Failed to get collaboration insights:', error)
      return []
    }
  }

  getCollaborationSession(sessionId: string): CollaborationSession | undefined {
    return this.collaborationSessions.get(sessionId)
  }

  getAllCollaborationSessions(): CollaborationSession[] {
    return Array.from(this.collaborationSessions.values())
  }

  setCurrentUser(userId: string, userName: string): void {
    this.currentUserId = userId
    this.currentUserName = userName
  }

  // Utility methods
  getConnectionStatus(): boolean {
    return this.isConnected
  }

  getConnectionId(): string | null {
    return this.connection?.connectionId || null
  }

  getConnectionState(): string {
    return this.connection?.state || 'Disconnected'
  }
}

// Create singleton instance
export const realtimeService = new RealtimeService()

// Auto-connect when the service is imported
if (typeof window !== 'undefined') {
  realtimeService.connect().catch(console.error)
}
