import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { jwtVerify } from 'jose'

/**
 * Enhanced Authentication Middleware for Next.js 15
 * Handles Azure AD B2C authentication, route protection, and security headers
 */

// Configuration
const AUTH_COOKIE_NAME = 'msal_session' // Updated to match B2C implementation
const REFRESH_COOKIE_NAME = 'refresh_token'
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key')

// Define route patterns
const AUTH_ROUTES = ["/auth/login", "/auth/register", "/auth/signup", "/auth/forgot-password", "/auth/callback"];
const PUBLIC_ROUTES = ["/", "/about", "/contact", "/privacy", "/terms"];
const PROTECTED_ROUTES = ["/app", "/dashboard", "/documents", "/projects", "/templates", "/workflows", "/analytics"];
const ADMIN_ROUTES = ["/admin", "/app/admin"];

// Helper functions
function isPublicRoute(pathname: string): boolean {
  return PUBLIC_ROUTES.includes(pathname) ||
         pathname.startsWith("/_next") ||
         pathname.startsWith("/favicon.ico") ||
         pathname.startsWith("/images") ||
         pathname.startsWith("/fonts") ||
         pathname.startsWith("/.well-known") ||
         pathname.startsWith("/api/auth") ||
         pathname.startsWith("/api/health") ||
         pathname.startsWith("/robots.txt") ||
         pathname.startsWith("/sitemap.xml")
}

function isAuthRoute(pathname: string): boolean {
  return AUTH_ROUTES.some(route => pathname.startsWith(route))
}

function isAdminRoute(pathname: string): boolean {
  return ADMIN_ROUTES.some(route => pathname.startsWith(route))
}

function isApiRoute(pathname: string): boolean {
  return pathname.startsWith("/api")
}

function isProtectedRoute(pathname: string): boolean {
  return PROTECTED_ROUTES.some(route => pathname.startsWith(route))
}

async function verifyToken(token: string): Promise<any> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET)
    return payload
  } catch (error) {
    // Fallback to basic JWT decode for development
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      const expiresAt = payload.exp * 1000
      if (Date.now() >= expiresAt) {
        return null
      }
      return payload
    } catch {
      return null
    }
  }
}

function hasRequiredRole(userRoles: string[], requiredRoles: string[]): boolean {
  if (!requiredRoles.length) return true
  return requiredRoles.some(role => userRoles.includes(role))
}

function addSecurityHeaders(response: NextResponse): NextResponse {
  // Security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('X-XSS-Protection', '1; mode=block')

  // CSP header for Azure AD B2C and local development
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://login.microsoftonline.com https://*.b2clogin.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "connect-src 'self' http://localhost:7071 https://login.microsoftonline.com https://*.b2clogin.com https://graph.microsoft.com wss: ws:",
    "frame-src 'self' https://login.microsoftonline.com https://*.b2clogin.com",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self' https://login.microsoftonline.com https://*.b2clogin.com",
  ].join('; ')

  response.headers.set('Content-Security-Policy', csp)

  // HSTS header (only in production)
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')
  }

  return response
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for public routes
  if (isPublicRoute(pathname)) {
    const response = NextResponse.next()
    return addSecurityHeaders(response)
  }

  // Get tokens from cookies and headers
  const msalSession = request.cookies.get(AUTH_COOKIE_NAME)?.value
  const authToken = request.headers.get("authorization")?.replace("Bearer ", "")
  const refreshToken = request.cookies.get(REFRESH_COOKIE_NAME)?.value

  let isAuthenticated = false
  let userPayload: any = null

  // Check MSAL session cookie for B2C authentication
  if (msalSession) {
    try {
      const sessionData = JSON.parse(msalSession)
      isAuthenticated = sessionData.isAuthenticated &&
                       sessionData.expiresOn &&
                       Date.now() < sessionData.expiresOn

      if (isAuthenticated) {
        // Get user info from separate cookie
        const userInfoCookie = request.cookies.get('msal_user_info')?.value
        if (userInfoCookie) {
          userPayload = JSON.parse(userInfoCookie)
        }
      }
    } catch (error) {
      console.warn('Failed to parse MSAL session:', error)
      isAuthenticated = false
    }
  } else if (authToken) {
    // Fallback to JWT token verification
    userPayload = await verifyToken(authToken)
    isAuthenticated = !!userPayload
  }

  // Handle API routes
  if (isApiRoute(pathname)) {
    // Protected API routes require authentication
    if (!pathname.startsWith("/api/auth") &&
        !pathname.startsWith("/api/health") &&
        !isAuthenticated) {
      return new NextResponse(
        JSON.stringify({
          error: "Unauthorized",
          message: "Authentication required"
        }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" }
        }
      )
    }

    // Add user context to request headers for API routes
    if (isAuthenticated && userPayload) {
      const response = NextResponse.next()
      response.headers.set('x-user-id', userPayload.sub || userPayload.userId || '')
      response.headers.set('x-user-email', userPayload.email || '')
      response.headers.set('x-user-roles', JSON.stringify(userPayload.roles || []))
      response.headers.set('x-organization-id', userPayload.organizationId || '')
      return addSecurityHeaders(response)
    }

    return addSecurityHeaders(NextResponse.next())
  }

  // Handle protected routes
  if (isProtectedRoute(pathname)) {
    if (!isAuthenticated) {
      // Redirect to enhanced login
      const loginUrl = new URL("/auth/enhanced-login", request.url)
      loginUrl.searchParams.set("returnUrl", pathname)
      return NextResponse.redirect(loginUrl)
    }

    // Check admin routes
    if (isAdminRoute(pathname)) {
      const userRoles = userPayload?.roles || []
      const adminRoles = ['admin', 'super_admin', 'system_admin']

      if (!hasRequiredRole(userRoles, adminRoles)) {
        return new NextResponse(
          '<html><body><h1>403 - Forbidden</h1><p>You do not have permission to access this resource.</p></body></html>',
          { status: 403, headers: { 'Content-Type': 'text/html' } }
        )
      }
    }

    // Add user context to request headers
    const response = NextResponse.next()
    if (userPayload) {
      response.headers.set('x-user-id', userPayload.sub || userPayload.userId || '')
      response.headers.set('x-user-email', userPayload.email || '')
      response.headers.set('x-user-roles', JSON.stringify(userPayload.roles || []))
      response.headers.set('x-organization-id', userPayload.organizationId || '')
    }
    response.headers.set("X-User-Authenticated", "true")

    return addSecurityHeaders(response)
  }

  // Redirect to dashboard if accessing auth routes while authenticated
  if (isAuthRoute(pathname) && isAuthenticated) {
    return NextResponse.redirect(new URL("/app/dashboard", request.url))
  }

  // For all other routes, just add security headers
  const response = NextResponse.next()
  return addSecurityHeaders(response)
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
};
