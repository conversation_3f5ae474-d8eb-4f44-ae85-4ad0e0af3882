/**
 * Production-Ready MSAL Authentication Service
 * Comprehensive Azure AD B2C integration with all user flow policies
 * Supports popup and redirect flows with proper error handling
 */

import {
  PublicClientApplication,
  Configuration,
  LogLevel,
  AuthenticationResult,
  PopupRequest,
  RedirectRequest,
  SilentRequest,
  EndSessionRequest,
  AccountInfo,
  InteractionRequiredAuthError,
  BrowserAuthError,
  AuthError
} from '@azure/msal-browser'
import type { StandardToken, UserContext } from '@/types/backend'

// B2C Policy Types
export enum B2CPolicyType {
  SIGN_IN = 'signin',
  SIGN_UP = 'signup', 
  SIGN_UP_SIGN_IN = 'signupsignin',
  PASSWORD_RESET = 'passwordreset',
  PROFILE_EDIT = 'profileedit'
}

// Authentication Method Types
export enum AuthMethod {
  POPUP = 'popup',
  REDIRECT = 'redirect'
}

// MSAL Configuration Interface
interface MSALConfig {
  clientId: string
  tenantName: string
  authorityDomain: string
  policies: {
    signIn: string
    signUp: string
    signUpSignIn: string
    passwordReset: string
    profileEdit: string
  }
  redirectUri: string
  postLogoutRedirectUri: string
  scopes: string[]
}

// Load configuration from environment variables
const MSAL_CONFIG: MSALConfig = {
  clientId: process.env.NEXT_PUBLIC_AZURE_AD_B2C_CLIENT_ID || '',
  tenantName: process.env.NEXT_PUBLIC_AZURE_AD_B2C_TENANT_NAME || '',
  authorityDomain: process.env.NEXT_PUBLIC_AZURE_AD_B2C_AUTHORITY_DOMAIN || '',
  policies: {
    signIn: process.env.NEXT_PUBLIC_AZURE_AD_B2C_SIGNIN_POLICY || 'B2C_1_SI',
    signUp: process.env.NEXT_PUBLIC_AZURE_AD_B2C_SIGNUP_POLICY || 'B2C_1_SU',
    signUpSignIn: process.env.NEXT_PUBLIC_AZURE_AD_B2C_SUSI_POLICY || 'B2C_1_SUSI',
    passwordReset: process.env.NEXT_PUBLIC_AZURE_AD_B2C_PASSWORD_RESET_POLICY || 'B2C_1_passwordreset1',
    profileEdit: process.env.NEXT_PUBLIC_AZURE_AD_B2C_PROFILE_EDIT_POLICY || 'B2C_1_profileedit1'
  },
  redirectUri: process.env.NEXT_PUBLIC_AZURE_AD_B2C_REDIRECT_URI || 'http://localhost:3000/auth/callback',
  postLogoutRedirectUri: process.env.NEXT_PUBLIC_AZURE_AD_B2C_POST_LOGOUT_REDIRECT_URI || 'http://localhost:3000',
  scopes: [
    'openid',
    'profile',
    'email',
    'offline_access'
  ]
}

// Validate configuration
const validateConfig = (): void => {
  const required = ['clientId', 'tenantName', 'authorityDomain']
  const missing = required.filter(key => !MSAL_CONFIG[key as keyof MSALConfig])
  
  if (missing.length > 0) {
    throw new Error(`Missing required MSAL configuration: ${missing.join(', ')}`)
  }
}

// Authority URL builder
const getAuthorityUrl = (policy: string): string => {
  return `https://${MSAL_CONFIG.authorityDomain}/${MSAL_CONFIG.tenantName}.onmicrosoft.com/${policy}`
}

// MSAL Configuration
const msalConfig: Configuration = {
  auth: {
    clientId: MSAL_CONFIG.clientId,
    authority: getAuthorityUrl(MSAL_CONFIG.policies.signUpSignIn),
    knownAuthorities: [MSAL_CONFIG.authorityDomain],
    redirectUri: MSAL_CONFIG.redirectUri,
    postLogoutRedirectUri: MSAL_CONFIG.postLogoutRedirectUri,
    navigateToLoginRequestUrl: false,
  },
  cache: {
    cacheLocation: 'localStorage',
    storeAuthStateInCookie: false,
  },
  system: {
    loggerOptions: {
      loggerCallback: (level, message, containsPii) => {
        if (containsPii) return
        
        switch (level) {
          case LogLevel.Error:
            console.error('[MSAL Error]', message)
            break
          case LogLevel.Warning:
            console.warn('[MSAL Warning]', message)
            break
          case LogLevel.Info:
            if (process.env.NODE_ENV === 'development') {
              console.info('[MSAL Info]', message)
            }
            break
          case LogLevel.Verbose:
            if (process.env.NODE_ENV === 'development') {
              console.debug('[MSAL Verbose]', message)
            }
            break
        }
      },
      logLevel: process.env.NODE_ENV === 'development' ? LogLevel.Verbose : LogLevel.Warning,
      piiLoggingEnabled: false,
    },
    windowHashTimeout: 60000,
    iframeHashTimeout: 6000,
    loadFrameTimeout: 0,
    asyncPopups: false
  }
}

// Create MSAL instance
let msalInstance: PublicClientApplication | null = null

// Initialize MSAL instance
const initializeMSAL = async (): Promise<PublicClientApplication> => {
  if (msalInstance) {
    return msalInstance
  }

  validateConfig()
  
  msalInstance = new PublicClientApplication(msalConfig)
  
  try {
    await msalInstance.initialize()
    
    // Handle redirect promise on app startup
    const response = await msalInstance.handleRedirectPromise()
    if (response) {
      console.log('[MSAL] Redirect response received:', response.account?.username)
    }
    
    return msalInstance
  } catch (error) {
    console.error('[MSAL] Initialization failed:', error)
    throw new Error(`MSAL initialization failed: ${error}`)
  }
}

// Request builders for different policies
const createLoginRequest = (policy: string, method: AuthMethod = AuthMethod.POPUP): PopupRequest | RedirectRequest => ({
  scopes: MSAL_CONFIG.scopes,
  authority: getAuthorityUrl(policy),
  prompt: 'select_account',
  extraQueryParameters: {},
})

const createSilentRequest = (account: AccountInfo, policy: string): SilentRequest => ({
  scopes: MSAL_CONFIG.scopes,
  authority: getAuthorityUrl(policy),
  account: account,
  forceRefresh: false,
})

// Convert MSAL result to StandardToken
const convertToStandardToken = (result: AuthenticationResult): StandardToken => {
  const claims = result.idTokenClaims as any || {}
  
  return {
    accessToken: result.accessToken,
    refreshToken: '', // B2C doesn't provide refresh tokens in browser
    userId: result.account?.localAccountId || claims.sub || '',
    email: result.account?.username || claims.email || '',
    roles: claims.roles || claims.extension_roles || [],
    organizationIds: claims.organizations || claims.extension_organizations || [],
    tenantId: claims.tid || '',
    expiresAt: result.expiresOn?.getTime() || (Date.now() + 3600000), // 1 hour default
    scope: result.scopes?.join(' ') || '',
    tokenType: 'Bearer'
  }
}

// Convert MSAL account to UserContext
const convertToUserContext = (account: AccountInfo, claims?: any): UserContext => {
  const idTokenClaims = claims || {}
  
  return {
    id: account.localAccountId || idTokenClaims.sub || '',
    email: account.username || idTokenClaims.email || '',
    name: account.name || idTokenClaims.name || idTokenClaims.given_name || '',
    roles: idTokenClaims.roles || idTokenClaims.extension_roles || [],
    organizationIds: idTokenClaims.organizations || idTokenClaims.extension_organizations || [],
    tenantId: idTokenClaims.tid || '',
    permissions: idTokenClaims.permissions || idTokenClaims.extension_permissions || [],
    profilePictureUrl: idTokenClaims.picture || undefined,
    lastLoginAt: new Date().toISOString(),
    isEmailVerified: idTokenClaims.email_verified || false,
    locale: idTokenClaims.locale || 'en-US'
  }
}

/**
 * Production-Ready MSAL Authentication Service
 */
export class MSALAuthService {
  private msal: PublicClientApplication | null = null
  private initialized = false

  /**
   * Initialize the MSAL service
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      this.msal = await initializeMSAL()
      this.initialized = true
      console.log('[MSAL Service] Initialized successfully')
    } catch (error) {
      console.error('[MSAL Service] Initialization failed:', error)
      throw error
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    if (!this.msal) return false
    
    const accounts = this.msal.getAllAccounts()
    return accounts.length > 0
  }

  /**
   * Get current account
   */
  getCurrentAccount(): AccountInfo | null {
    if (!this.msal) return null
    
    const accounts = this.msal.getAllAccounts()
    return accounts.length > 0 ? accounts[0] : null
  }

  /**
   * Get current user context
   */
  async getCurrentUser(): Promise<UserContext | null> {
    const account = this.getCurrentAccount()
    if (!account) return null

    try {
      // Try to get fresh token to get latest claims
      const silentRequest = createSilentRequest(account, MSAL_CONFIG.policies.signUpSignIn)
      const result = await this.msal!.acquireTokenSilent(silentRequest)
      
      return convertToUserContext(account, result.idTokenClaims)
    } catch (error) {
      // If silent request fails, use cached account info
      return convertToUserContext(account)
    }
  }

  /**
   * Get access token
   */
  async getAccessToken(): Promise<string | null> {
    if (!this.msal) return null

    const account = this.getCurrentAccount()
    if (!account) return null

    try {
      const silentRequest = createSilentRequest(account, MSAL_CONFIG.policies.signUpSignIn)
      const result = await this.msal.acquireTokenSilent(silentRequest)
      return result.accessToken
    } catch (error) {
      console.warn('[MSAL Service] Silent token acquisition failed:', error)
      return null
    }
  }

  /**
   * Sign in with specified policy
   */
  async signIn(policy: B2CPolicyType = B2CPolicyType.SIGN_UP_SIGN_IN, method: AuthMethod = AuthMethod.POPUP): Promise<StandardToken> {
    if (!this.msal) {
      throw new Error('MSAL not initialized')
    }

    const policyName = this.getPolicyName(policy)
    const loginRequest = createLoginRequest(policyName, method)

    try {
      let result: AuthenticationResult

      if (method === AuthMethod.POPUP) {
        result = await this.msal.loginPopup(loginRequest)
      } else {
        await this.msal.loginRedirect(loginRequest)
        // For redirect, we'll handle the result in handleRedirectPromise
        throw new Error('Redirect in progress')
      }

      return convertToStandardToken(result)
    } catch (error) {
      console.error('[MSAL Service] Sign in failed:', error)
      throw this.handleAuthError(error)
    }
  }

  /**
   * Sign up (uses sign up policy)
   */
  async signUp(method: AuthMethod = AuthMethod.POPUP): Promise<StandardToken> {
    return this.signIn(B2CPolicyType.SIGN_UP, method)
  }

  /**
   * Sign out
   */
  async signOut(method: AuthMethod = AuthMethod.POPUP): Promise<void> {
    if (!this.msal) {
      throw new Error('MSAL not initialized')
    }

    const account = this.getCurrentAccount()
    if (!account) return

    const logoutRequest: EndSessionRequest = {
      account: account,
      postLogoutRedirectUri: MSAL_CONFIG.postLogoutRedirectUri,
    }

    try {
      if (method === AuthMethod.POPUP) {
        await this.msal.logoutPopup(logoutRequest)
      } else {
        await this.msal.logoutRedirect(logoutRequest)
      }
    } catch (error) {
      console.error('[MSAL Service] Sign out failed:', error)
      throw this.handleAuthError(error)
    }
  }

  /**
   * Reset password
   */
  async resetPassword(method: AuthMethod = AuthMethod.POPUP): Promise<void> {
    if (!this.msal) {
      throw new Error('MSAL not initialized')
    }

    const resetRequest = createLoginRequest(MSAL_CONFIG.policies.passwordReset, method)

    try {
      if (method === AuthMethod.POPUP) {
        await this.msal.loginPopup(resetRequest)
      } else {
        await this.msal.loginRedirect(resetRequest)
      }
    } catch (error) {
      console.error('[MSAL Service] Password reset failed:', error)
      throw this.handleAuthError(error)
    }
  }

  /**
   * Edit profile
   */
  async editProfile(method: AuthMethod = AuthMethod.POPUP): Promise<StandardToken | void> {
    if (!this.msal) {
      throw new Error('MSAL not initialized')
    }

    const editRequest = createLoginRequest(MSAL_CONFIG.policies.profileEdit, method)

    try {
      let result: AuthenticationResult

      if (method === AuthMethod.POPUP) {
        result = await this.msal.loginPopup(editRequest)
        return convertToStandardToken(result)
      } else {
        await this.msal.loginRedirect(editRequest)
      }
    } catch (error) {
      console.error('[MSAL Service] Profile edit failed:', error)
      throw this.handleAuthError(error)
    }
  }

  /**
   * Handle redirect promise (call this on app startup)
   */
  async handleRedirectPromise(): Promise<AuthenticationResult | null> {
    if (!this.msal) {
      throw new Error('MSAL not initialized')
    }

    if (!this.initialized) {
      throw new Error('MSAL service not initialized')
    }

    try {
      return await this.msal.handleRedirectPromise()
    } catch (error) {
      console.error('[MSAL Service] Redirect promise handling failed:', error)
      throw this.handleAuthError(error)
    }
  }

  /**
   * Get policy name from enum
   */
  private getPolicyName(policy: B2CPolicyType): string {
    switch (policy) {
      case B2CPolicyType.SIGN_IN:
        return MSAL_CONFIG.policies.signIn
      case B2CPolicyType.SIGN_UP:
        return MSAL_CONFIG.policies.signUp
      case B2CPolicyType.SIGN_UP_SIGN_IN:
        return MSAL_CONFIG.policies.signUpSignIn
      case B2CPolicyType.PASSWORD_RESET:
        return MSAL_CONFIG.policies.passwordReset
      case B2CPolicyType.PROFILE_EDIT:
        return MSAL_CONFIG.policies.profileEdit
      default:
        return MSAL_CONFIG.policies.signUpSignIn
    }
  }

  /**
   * Handle authentication errors
   */
  private handleAuthError(error: any): Error {
    if (error instanceof InteractionRequiredAuthError) {
      return new Error('User interaction required. Please sign in again.')
    }

    if (error instanceof BrowserAuthError) {
      if (error.errorCode === 'popup_window_error') {
        return new Error('Popup was blocked. Please allow popups and try again.')
      }
      if (error.errorCode === 'user_cancelled') {
        return new Error('Sign in was cancelled.')
      }
      return new Error(`Browser error: ${error.errorMessage}`)
    }

    if (error instanceof AuthError) {
      return new Error(`Authentication error: ${error.errorMessage}`)
    }

    return new Error(error?.message || 'An unexpected error occurred during authentication.')
  }
}

// Export singleton instance
export const msalAuthService = new MSALAuthService()

// Export configuration for reference
export { MSAL_CONFIG }
