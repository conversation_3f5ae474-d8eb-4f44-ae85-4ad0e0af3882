/**
 * Server-side Azure AD B2C Authentication API
 * Handles authentication with client secret securely on the server
 */

import { NextRequest, NextResponse } from 'next/server'
import { ConfidentialClientApplication, Configuration, AuthenticationResult } from '@azure/msal-node'

// Server-side MSAL configuration with client secret
const msalConfig: Configuration = {
  auth: {
    clientId: process.env.AZURE_AD_B2C_CLIENT_ID!,
    clientSecret: process.env.AZURE_AD_B2C_CLIENT_SECRET!,
    authority: `https://${process.env.AZURE_AD_B2C_AUTHORITY_DOMAIN}/${process.env.AZURE_AD_B2C_TENANT_NAME}.onmicrosoft.com/${process.env.NEXT_PUBLIC_AZURE_AD_B2C_SUSI_POLICY}`,
  },
  system: {
    loggerOptions: {
      loggerCallback: (level, message, containsPii) => {
        if (containsPii) return
        console.log(`[MSAL Server] ${message}`)
      },
      piiLoggingEnabled: false,
      logLevel: 3, // Info level
    },
  },
}

// Create MSAL instance
const msalInstance = new ConfidentialClientApplication(msalConfig)

export async function POST(request: NextRequest) {
  try {
    const { policy = 'B2C_1_SUSI' } = await request.json()

    // Build authority URL for the specific policy
    const authority = `https://${process.env.AZURE_AD_B2C_AUTHORITY_DOMAIN}/${process.env.AZURE_AD_B2C_TENANT_NAME}.onmicrosoft.com/${policy}`
    
    // Get authorization URL for the client-side redirect
    const authCodeUrlParameters = {
      scopes: ['openid', 'profile', 'email'],
      redirectUri: process.env.NEXT_PUBLIC_AZURE_AD_B2C_REDIRECT_URI!,
      authority: authority,
      prompt: 'select_account',
    }

    const authUrl = await msalInstance.getAuthCodeUrl(authCodeUrlParameters)

    return NextResponse.json({
      success: true,
      authUrl: authUrl,
      policy: policy,
    })

  } catch (error) {
    console.error('[Auth API] Login failed:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed',
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const policy = searchParams.get('policy') || 'B2C_1_SUSI'

    // Build authority URL for the specific policy
    const authority = `https://${process.env.AZURE_AD_B2C_AUTHORITY_DOMAIN}/${process.env.AZURE_AD_B2C_TENANT_NAME}.onmicrosoft.com/${policy}`
    
    // Get authorization URL for the client-side redirect
    const authCodeUrlParameters = {
      scopes: ['openid', 'profile', 'email'],
      redirectUri: process.env.NEXT_PUBLIC_AZURE_AD_B2C_REDIRECT_URI!,
      authority: authority,
      prompt: 'select_account',
    }

    const authUrl = await msalInstance.getAuthCodeUrl(authCodeUrlParameters)

    // Redirect to Azure AD B2C
    return NextResponse.redirect(authUrl)

  } catch (error) {
    console.error('[Auth API] Login redirect failed:', error)
    
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_AZURE_AD_B2C_POST_LOGOUT_REDIRECT_URI}/auth/enhanced-login?error=auth_failed`
    )
  }
}
