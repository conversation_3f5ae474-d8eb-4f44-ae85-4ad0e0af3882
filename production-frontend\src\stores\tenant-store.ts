/**
 * Tenant Store
 * Manages multi-tenant state and context switching
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { storage } from '../lib/utils'
import type { Tenant } from '../types/tenant'
import type { ID, Timestamp } from '../types'

interface TenantStoreState {
  // State
  currentTenant: Tenant | null
  tenants: Tenant[]
  loading: boolean
  error: string | null
  lastUpdated?: Timestamp
  _hydrated: boolean

  // Actions
  setCurrentTenant: (tenant: Tenant | null) => void
  setTenants: (tenants: Tenant[]) => void
  addTenant: (tenant: Tenant) => void
  updateTenant: (tenantId: ID, updates: Partial<Tenant>) => void
  removeTenant: (tenantId: ID) => void
  switchTenant: (tenantId: ID) => Promise<void>
  fetchTenants: () => Promise<void>
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  reset: () => void

  // Computed
  getTenantById: (tenantId: ID) => Tenant | undefined
  isCurrentTenant: (tenantId: ID) => boolean
  hasMultipleTenants: () => boolean
}

export const useTenantStore = create<TenantStoreState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentTenant: null,
      tenants: [],
      loading: false,
      error: null,
      lastUpdated: undefined,
      _hydrated: false,

      // Actions
      setCurrentTenant: (tenant) => {
        set({
          currentTenant: tenant,
          lastUpdated: new Date().toISOString(),
        })
      },

      setTenants: (tenants) => {
        set({
          tenants,
          lastUpdated: new Date().toISOString(),
        })
      },

      addTenant: (tenant) => {
        set((state) => ({
          tenants: [...state.tenants, tenant],
          lastUpdated: new Date().toISOString(),
        }))
      },

      updateTenant: (tenantId, updates) => {
        set((state) => ({
          tenants: state.tenants.map(tenant =>
            tenant.id === tenantId ? { ...tenant, ...updates } : tenant
          ),
          currentTenant: state.currentTenant?.id === tenantId
            ? { ...state.currentTenant, ...updates }
            : state.currentTenant,
          lastUpdated: new Date().toISOString(),
        }))
      },

      removeTenant: (tenantId) => {
        set((state) => ({
          tenants: state.tenants.filter(tenant => tenant.id !== tenantId),
          currentTenant: state.currentTenant?.id === tenantId ? null : state.currentTenant,
          lastUpdated: new Date().toISOString(),
        }))
      },

      switchTenant: async (tenantId) => {
        const { tenants, setCurrentTenant, setError } = get()
        const tenant = tenants.find(t => t.id === tenantId)
        
        if (!tenant) {
          setError(`Tenant with ID ${tenantId} not found`)
          return
        }

        try {
          setCurrentTenant(tenant)
          setError(null)
          
          // Here you could add API call to switch tenant context on backend
          // await tenantService.switchTenant(tenantId)
          
        } catch (error) {
          setError(error instanceof Error ? error.message : 'Failed to switch tenant')
          throw error
        }
      },

      fetchTenants: async () => {
        const { setLoading, setError, setTenants } = get()
        
        setLoading(true)
        setError(null)

        try {
          // Call the tenant service API
          const response = await fetch('/api/tenants', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            }
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch tenants: ${response.statusText}`);
          }

          const data = await response.json();
          setTenants(data.tenants || []);

        } catch (error) {
          setError(error instanceof Error ? error.message : 'Failed to fetch tenants')
        } finally {
          setLoading(false)
        }
      },

      setLoading: (loading) => {
        set({ loading })
      },

      setError: (error) => {
        set({ error })
      },

      reset: () => {
        set({
          currentTenant: null,
          tenants: [],
          loading: false,
          error: null,
          lastUpdated: undefined,
        })
      },

      // Computed
      getTenantById: (tenantId) => {
        return get().tenants.find(tenant => tenant.id === tenantId)
      },

      isCurrentTenant: (tenantId) => {
        return get().currentTenant?.id === tenantId
      },

      hasMultipleTenants: () => {
        return get().tenants.length > 1
      },
    }),
    {
      name: 'tenant-store',
      storage: createJSONStorage(() => ({
        getItem: (name) => storage.get(name, null),
        setItem: (name, value) => storage.set(name, value),
        removeItem: (name) => storage.remove(name),
      })),
      partialize: (state) => ({
        currentTenant: state.currentTenant,
        tenants: state.tenants.slice(0, 20), // Limit persisted tenants
        lastUpdated: state.lastUpdated,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state._hydrated = true
        }
      },
    }
  )
)

// Selector hooks
export const useCurrentTenant = () => useTenantStore((state) => state.currentTenant)
export const useTenants = () => useTenantStore((state) => state.tenants)
export const useTenantLoading = () => useTenantStore((state) => state.loading)
export const useTenantError = () => useTenantStore((state) => state.error)

// Action hooks
export const useSwitchTenant = () => useTenantStore((state) => state.switchTenant)
export const useFetchTenants = () => useTenantStore((state) => state.fetchTenants)

// Computed hooks
export const useHasMultipleTenants = () => useTenantStore((state) => state.hasMultipleTenants())

// Main tenant hook for components
export const useTenant = () => {
  const currentTenant = useCurrentTenant()
  const tenants = useTenants()
  const loading = useTenantLoading()
  const error = useTenantError()
  const switchTenant = useSwitchTenant()
  const fetchTenants = useFetchTenants()
  const hasMultipleTenants = useHasMultipleTenants()

  return {
    currentTenant,
    tenants,
    isLoading: loading,
    error,
    switchTenant,
    fetchTenants,
    hasMultipleTenants,
  }
}

// Initialize tenant store function
export const initializeTenantStore = async () => {
  const { fetchTenants } = useTenantStore.getState()
  await fetchTenants()
}
