'use client'

/**
 * Authentication Callback Page
 * Handles MSAL redirect callbacks for Azure AD B2C
 */

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Loader2, CheckCircle2, AlertCircle } from 'lucide-react'
import { msalAuthService } from '@/lib/auth/msal-service'
import { useAuthStore } from '@/stores/auth-store'
import { useNotificationStore } from '@/stores/notification-store'
import type { StandardToken } from '@/types/backend'

export default function AuthCallbackPage() {
  const router = useRouter()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('Processing authentication...')
  
  const { setToken, updateProfile } = useAuthStore()
  const { addNotification } = useNotificationStore()

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Initialize MSAL if not already done
        await msalAuthService.initialize()
        
        // Handle the redirect promise
        const result = await msalAuthService.handleRedirectPromise()
        
        if (result) {
          // Authentication successful
          const tokenData: StandardToken = {
            accessToken: result.accessToken,
            refreshToken: '', // B2C doesn't provide refresh tokens in browser
            userId: result.account?.localAccountId || '',
            email: result.account?.username || '',
            roles: (result.idTokenClaims as any)?.roles || [],
            organizationIds: (result.idTokenClaims as any)?.organizations || [],
            tenantId: (result.idTokenClaims as any)?.tid || '',
            expiresAt: result.expiresOn?.getTime() || (Date.now() + 3600000),
            scope: result.scopes?.join(' ') || '',
            tokenType: 'Bearer'
          }
          
          // Update auth store
          setToken(tokenData)
          
          // Get user profile
          const userProfile = await msalAuthService.getCurrentUser()
          if (userProfile) {
            await updateProfile(userProfile)
          }
          
          setStatus('success')
          setMessage('Authentication successful! Redirecting...')
          
          // Show success notification
          addNotification({
            type: 'success',
            title: 'Welcome!',
            message: `Successfully signed in as ${tokenData.email}`
          })
          
          // Redirect to dashboard after a short delay
          setTimeout(() => {
            router.push('/dashboard')
          }, 2000)
          
        } else {
          // No result - might be initial page load
          setStatus('error')
          setMessage('No authentication result received.')
          
          setTimeout(() => {
            router.push('/auth/enhanced-login')
          }, 3000)
        }
        
      } catch (error) {
        console.error('[Auth Callback] Error handling authentication:', error)
        
        setStatus('error')
        setMessage(error instanceof Error ? error.message : 'Authentication failed')
        
        // Show error notification
        addNotification({
          type: 'error',
          title: 'Authentication Failed',
          message: error instanceof Error ? error.message : 'Authentication failed'
        })
        
        // Redirect to login after delay
        setTimeout(() => {
          router.push('/auth/enhanced-login')
        }, 3000)
      }
    }

    handleCallback()
  }, [router, setToken, updateProfile, addNotification])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center space-y-4 max-w-md mx-auto p-8">
        {status === 'loading' && (
          <>
            <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto" />
            <h2 className="text-xl font-semibold text-gray-900">Processing Authentication</h2>
            <p className="text-gray-600">{message}</p>
          </>
        )}
        
        {status === 'success' && (
          <>
            <CheckCircle2 className="h-12 w-12 text-green-600 mx-auto" />
            <h2 className="text-xl font-semibold text-gray-900">Authentication Successful</h2>
            <p className="text-gray-600">{message}</p>
          </>
        )}
        
        {status === 'error' && (
          <>
            <AlertCircle className="h-12 w-12 text-red-600 mx-auto" />
            <h2 className="text-xl font-semibold text-gray-900">Authentication Failed</h2>
            <p className="text-gray-600">{message}</p>
            <p className="text-sm text-gray-500">Redirecting to login page...</p>
          </>
        )}
      </div>
    </div>
  )
}
