'use client'

/**
 * Enhanced Authentication Provider
 * Comprehensive Azure AD B2C integration with NextAuth and all policies support
 */

import React, { createContext, useContext, useEffect, useState } from 'react'
import { SessionProvider, useSession } from 'next-auth/react'
import { enhancedAzureAdB2CAuthService } from '@/lib/auth/enhanced-azure-ad-b2c'
import { useAuthStore } from '@/stores/auth-store'
import { useNotificationStore } from '@/stores/notification-store'
import type { StandardToken, UserContext } from '@/types/backend'

// Enhanced Auth Context Interface
interface EnhancedAuthContextType {
  // State
  isInitialized: boolean
  isAuthenticated: boolean
  isLoading: boolean
  user: UserContext | null
  token: StandardToken | null
  error: string | null

  // Methods
  initialize: () => Promise<void>
  syncWithSession: () => Promise<void>
  handleAuthCallback: () => Promise<void>
  clearError: () => void
}

const EnhancedAuthContext = createContext<EnhancedAuthContextType | undefined>(undefined)

interface EnhancedAuthProviderProps {
  children: React.ReactNode
}

/**
 * Enhanced Auth Provider Implementation
 */
function EnhancedAuthProviderImpl({ children }: EnhancedAuthProviderProps) {
  const { data: session, status } = useSession()
  const [isInitialized, setIsInitialized] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Zustand stores
  const {
    user,
    token,
    isAuthenticated,
    updateProfile: storeUpdateProfile,
    setToken: storeSetToken,
    clearAuth: storeClearAuth,
    checkAuthStatus: storeCheckAuthStatus
  } = useAuthStore()

  const { addNotification } = useNotificationStore()

  /**
   * Initialize the enhanced authentication service
   */
  const initialize = async () => {
    if (isInitialized) return

    try {
      setIsLoading(true)
      setError(null)

      // Initialize Azure AD B2C service
      await enhancedAzureAdB2CAuthService.initialize()

      // Check if user is authenticated with B2C
      if (enhancedAzureAdB2CAuthService.isAuthenticated()) {
        await syncWithB2C()
      }

      setIsInitialized(true)
      console.log('Enhanced authentication provider initialized')
    } catch (err: any) {
      console.error('Failed to initialize enhanced auth provider:', err)
      setError(err.message || 'Authentication initialization failed')
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Sync authentication state with Azure AD B2C
   */
  const syncWithB2C = async () => {
    try {
      const currentUser = await enhancedAzureAdB2CAuthService.getCurrentUser()
      const accessToken = await enhancedAzureAdB2CAuthService.getAccessToken()

      if (currentUser && accessToken) {
        // Update user profile in store
        await storeUpdateProfile(currentUser)

        // Create and set token
        const tokenData: StandardToken = {
          accessToken,
          userId: currentUser.id,
          email: currentUser.email,
          roles: currentUser.roles || [],
          organizationIds: currentUser.organizationIds || [],
          tenantId: currentUser.tenantId || '',
          expiresAt: Date.now() + 3600000 // 1 hour default
        }

        storeSetToken(tokenData)
        console.log('Authentication state synced with Azure AD B2C')
      }
    } catch (err) {
      console.error('Failed to sync with Azure AD B2C:', err)
    }
  }

  /**
   * Sync with NextAuth session
   */
  const syncWithSession = async () => {
    try {
      if (session?.user) {
        // If we have a NextAuth session but no B2C authentication,
        // try to sync or re-authenticate
        if (!enhancedAzureAdB2CAuthService.isAuthenticated()) {
          console.log('NextAuth session found but no B2C authentication, attempting sync')
          // Could implement session-based token exchange here
        }
      } else if (!session && enhancedAzureAdB2CAuthService.isAuthenticated()) {
        // If we have B2C authentication but no NextAuth session,
        // clear the B2C state
        console.log('B2C authenticated but no NextAuth session, clearing auth state')
        await storeClearAuth()
      }
    } catch (err) {
      console.error('Failed to sync with session:', err)
    }
  }

  /**
   * Handle authentication callback (for redirect flows)
   */
  const handleAuthCallback = async () => {
    try {
      // This is automatically handled by the B2C service initialization
      // but we can add additional logic here if needed
      await syncWithB2C()
    } catch (err) {
      console.error('Failed to handle auth callback:', err)
    }
  }

  /**
   * Clear error state
   */
  const clearError = () => {
    setError(null)
  }

  // Initialize on mount
  useEffect(() => {
    initialize()
  }, [])

  // Sync with session changes
  useEffect(() => {
    if (status !== 'loading' && isInitialized) {
      syncWithSession()
    }
  }, [session, status, isInitialized])

  // Handle URL changes for redirect callbacks
  useEffect(() => {
    const handleUrlChange = () => {
      const url = new URL(window.location.href)
      
      // Check if this is an auth callback
      if (url.pathname.includes('/auth/callback') || url.searchParams.has('code')) {
        handleAuthCallback()
      }
    }

    // Check current URL
    handleUrlChange()

    // Listen for URL changes
    window.addEventListener('popstate', handleUrlChange)
    
    return () => {
      window.removeEventListener('popstate', handleUrlChange)
    }
  }, [isInitialized])

  // Auto-refresh token before expiry
  useEffect(() => {
    if (!isAuthenticated || !token) return

    const timeUntilExpiry = token.expiresAt - Date.now()
    const refreshTime = Math.max(timeUntilExpiry - 300000, 60000) // Refresh 5 minutes before expiry, minimum 1 minute

    const refreshTimer = setTimeout(async () => {
      try {
        const newAccessToken = await enhancedAzureAdB2CAuthService.getAccessToken()
        
        if (newAccessToken && user) {
          const newTokenData: StandardToken = {
            ...token,
            accessToken: newAccessToken,
            expiresAt: Date.now() + 3600000 // 1 hour
          }
          
          storeSetToken(newTokenData)
          console.log('Token refreshed automatically')
        }
      } catch (err) {
        console.error('Auto token refresh failed:', err)
      }
    }, refreshTime)

    return () => clearTimeout(refreshTimer)
  }, [token, isAuthenticated, user, storeSetToken])

  const contextValue: EnhancedAuthContextType = {
    // State
    isInitialized,
    isAuthenticated,
    isLoading,
    user,
    token,
    error,

    // Methods
    initialize,
    syncWithSession,
    handleAuthCallback,
    clearError,
  }

  return (
    <EnhancedAuthContext.Provider value={contextValue}>
      {children}
    </EnhancedAuthContext.Provider>
  )
}

/**
 * Enhanced Authentication Provider with SessionProvider wrapper
 */
export function EnhancedAuthProvider({ children }: EnhancedAuthProviderProps) {
  return (
    <SessionProvider>
      <EnhancedAuthProviderImpl>
        {children}
      </EnhancedAuthProviderImpl>
    </SessionProvider>
  )
}

/**
 * Hook to use Enhanced Auth Context
 */
export function useEnhancedAuthContext() {
  const context = useContext(EnhancedAuthContext)
  if (context === undefined) {
    throw new Error('useEnhancedAuthContext must be used within an EnhancedAuthProvider')
  }
  return context
}

/**
 * Authentication Guard Component
 */
interface AuthGuardProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  requireAuth?: boolean
  requiredRoles?: string[]
  requiredPermissions?: string[]
}

export function AuthGuard({ 
  children, 
  fallback = null, 
  requireAuth = true,
  requiredRoles = [],
  requiredPermissions = []
}: AuthGuardProps) {
  const { isAuthenticated, isLoading, user } = useEnhancedAuthContext()

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  // Check authentication requirement
  if (requireAuth && !isAuthenticated) {
    return fallback || (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Authentication Required</h2>
          <p className="text-muted-foreground">Please sign in to access this page.</p>
        </div>
      </div>
    )
  }

  // Check role requirements
  if (requiredRoles.length > 0 && user) {
    const hasRequiredRole = requiredRoles.some(role => user.roles?.includes(role))
    if (!hasRequiredRole) {
      return fallback || (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-muted-foreground">You don't have the required permissions to access this page.</p>
          </div>
        </div>
      )
    }
  }

  // Check permission requirements
  if (requiredPermissions.length > 0 && user) {
    const hasRequiredPermission = requiredPermissions.some(permission => 
      user.permissions?.includes(permission)
    )
    if (!hasRequiredPermission) {
      return fallback || (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-muted-foreground">You don't have the required permissions to access this page.</p>
          </div>
        </div>
      )
    }
  }

  return <>{children}</>
}

// Export for backward compatibility
export default EnhancedAuthProvider
