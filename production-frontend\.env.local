# =============================================================================
# CORE APPLICATION CONFIGURATION
# =============================================================================

# Node.js Environment
NODE_ENV=development
PORT=3000

# Application Metadata
NEXT_PUBLIC_APP_NAME=HEPZ
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT=development

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:7071
NEXT_PUBLIC_AZURE_FUNCTIONS_KEY=Pgnzm2D5n1JUxiQodQdmGDVFZfddgDv_Elxav4P-0YFFAzFuaff3Iw==

# Event Grid Configuration
NEXT_PUBLIC_EVENT_GRID_ENDPOINT=https://hepzeg.eastus-1.eventgrid.azure.net/api/events
NEXT_PUBLIC_EVENT_GRID_ENABLED=true

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key-here

# SignalR Configuration
NEXT_PUBLIC_SIGNALR_URL=Endpoint=https://hepztech.service.signalr.net;AccessKey=9SSBeg7BwC71eF74gZgCTSASGF5kNSUMTMIQbmbBlbdmQ0RLKLbGJQQJ99BEACYeBjFXJ3w3AAAAASRSrRRE;Version=1.0;

# =============================================================================
# AUTHENTICATION CONFIGURATION (ENHANCED)
# =============================================================================

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=hepz-nextauth-secret-key-development-only-change-in-production

# Azure AD B2C Configuration (Enhanced with multiple flows)
AZURE_AD_B2C_TENANT_NAME=hepzdocs
AZURE_AD_B2C_CLIENT_ID=a2a369c1-cf18-40e8-b41b-102b02482c51
AZURE_AD_B2C_CLIENT_SECRET=****************************************

# User Flows (Updated to separate Sign In and Sign Up)
AZURE_AD_B2C_SIGNIN_FLOW=B2C_1_SI
AZURE_AD_B2C_SIGNUP_FLOW=B2C_1_SU
AZURE_AD_B2C_PASSWORD_RESET_FLOW=B2C_1_passwordreset1
AZURE_AD_B2C_PROFILE_EDIT_FLOW=B2C_1_profileedit1

# Azure B2C Additional Configuration
AZURE_AD_B2C_TENANT_ID=4824d1a3-08f0-4377-aa1f-83ca62a2af46
AZURE_AD_B2C_AUTHORITY_DOMAIN=hepzdocs.b2clogin.com

# Custom Attributes for Subscription Integration
AZURE_AD_B2C_CUSTOM_ATTRIBUTES=extension_SubscriptionPlan,extension_SubscriptionStatus

# Public Environment Variables for Frontend (MSAL Configuration)
NEXT_PUBLIC_AZURE_AD_B2C_TENANT_NAME=hepzdocs
NEXT_PUBLIC_AZURE_AD_B2C_CLIENT_ID=a2a369c1-cf18-40e8-b41b-102b02482c51
NEXT_PUBLIC_AZURE_AD_B2C_AUTHORITY_DOMAIN=hepzdocs.b2clogin.com

# B2C User Flow Policies (Updated to match your configuration)
NEXT_PUBLIC_AZURE_AD_B2C_SIGNIN_POLICY=B2C_1_SI
NEXT_PUBLIC_AZURE_AD_B2C_SIGNUP_POLICY=B2C_1_SU
NEXT_PUBLIC_AZURE_AD_B2C_SUSI_POLICY=B2C_1_SUSI
NEXT_PUBLIC_AZURE_AD_B2C_PASSWORD_RESET_POLICY=B2C_1_passwordreset1
NEXT_PUBLIC_AZURE_AD_B2C_PROFILE_EDIT_POLICY=B2C_1_profileedit1

# MSAL Redirect URIs (for Azure Portal configuration)
NEXT_PUBLIC_AZURE_AD_B2C_REDIRECT_URI=http://localhost:3001/auth/callback
NEXT_PUBLIC_AZURE_AD_B2C_POST_LOGOUT_REDIRECT_URI=http://localhost:3001

# Logging
NEXT_PUBLIC_LOG_LEVEL=debug
NEXT_PUBLIC_LOGGING_ENDPOINT=http://localhost:7071

# Sentry Configuration (DISABLED)
# NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/4509399086006352
# NEXT_PUBLIC_APP_VERSION=1.0.0
# NEXT_PUBLIC_SENTRY_ENVIRONMENT=development

# Analytics
NEXT_PUBLIC_CLARITY_PROJECT_ID=your-clarity-project-id

# Subscription/Payment
NEXT_PUBLIC_LEMONSQUEEZY_API_KEY=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
NEXT_PUBLIC_LEMONSQUEEZY_STORE_ID=80076
NEXT_PUBLIC_LEMONSQUEEZY_WEBHOOK_SECRET=lemonmgmtsecret

NEXT_PUBLIC_SIGNALR_URL=http://localhost:7071/api

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_REAL_TIME=true

# MSAL Configuration
NEXT_PUBLIC_AZURE_AD_B2C_TENANT_NAME=hepzdocs
NEXT_PUBLIC_AZURE_AD_B2C_CLIENT_ID=a2a369c1-cf18-40e8-b41b-102b02482c51
NEXT_PUBLIC_AZURE_AD_B2C_AUTHORITY_DOMAIN=hepzdocs.b2clogin.com

# Redirect URIs
NEXT_PUBLIC_AZURE_AD_B2C_REDIRECT_URI=http://localhost:3000/auth/callback
NEXT_PUBLIC_AZURE_AD_B2C_POST_LOGOUT_REDIRECT_URI=http://localhost:3000