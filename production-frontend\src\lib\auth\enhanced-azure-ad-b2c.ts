/**
 * Enhanced Azure AD B2C Authentication Service
 * Comprehensive support for all B2C policies: sign-in, sign-up, password reset, profile edit
 */

import { 
  PublicClientApplication, 
  Configuration, 
  LogLevel,
  AuthenticationResult,
  PopupRequest,
  RedirectRequest,
  SilentRequest,
  EndSessionRequest,
  AccountInfo
} from '@azure/msal-browser'
import type { StandardToken, UserContext } from '@/types/backend'

// Azure AD B2C Policy Types
export enum B2CPolicyType {
  SIGN_UP_SIGN_IN = 'signUpSignIn',
  PASSWORD_RESET = 'passwordReset',
  EDIT_PROFILE = 'editProfile'
}

// Azure AD B2C Configuration Interface
interface B2CConfig {
  clientId: string
  tenantName: string
  policies: {
    signUpSignIn: string
    passwordReset: string
    editProfile: string
  }
  redirectUri: string
  postLogoutRedirectUri: string
  scopes: string[]
}

// Enhanced Azure AD B2C Configuration
const AZ<PERSON><PERSON>_AD_B2C_CONFIG: B2CConfig = {
  clientId: process.env.NEXT_PUBLIC_AZURE_AD_B2C_CLIENT_ID || '',
  tenantName: process.env.NEXT_PUBLIC_AZURE_AD_B2C_TENANT_NAME || '',
  policies: {
    signUpSignIn: process.env.NEXT_PUBLIC_AZURE_AD_B2C_SIGNUP_SIGNIN_POLICY || 'B2C_1_signupsignin',
    passwordReset: process.env.NEXT_PUBLIC_AZURE_AD_B2C_PASSWORD_RESET_POLICY || 'B2C_1_passwordreset',
    editProfile: process.env.NEXT_PUBLIC_AZURE_AD_B2C_EDIT_PROFILE_POLICY || 'B2C_1_editprofile'
  },
  redirectUri: process.env.NEXT_PUBLIC_AZURE_AD_B2C_REDIRECT_URI || 'http://localhost:3000/auth/callback',
  postLogoutRedirectUri: process.env.NEXT_PUBLIC_AZURE_AD_B2C_POST_LOGOUT_REDIRECT_URI || 'http://localhost:3000',
  scopes: [
    'openid',
    'profile',
    'email',
    'offline_access',
    `https://${process.env.NEXT_PUBLIC_AZURE_AD_B2C_TENANT_NAME}.onmicrosoft.com/api/read`,
    `https://${process.env.NEXT_PUBLIC_AZURE_AD_B2C_TENANT_NAME}.onmicrosoft.com/api/write`
  ]
}

// Authority URLs for different policies
const getAuthorityUrl = (policy: string): string => {
  return `https://${AZURE_AD_B2C_CONFIG.tenantName}.b2clogin.com/${AZURE_AD_B2C_CONFIG.tenantName}.onmicrosoft.com/${policy}`
}

// MSAL Configuration for Sign-Up/Sign-In Policy
const msalConfig: Configuration = {
  auth: {
    clientId: AZURE_AD_B2C_CONFIG.clientId,
    authority: getAuthorityUrl(AZURE_AD_B2C_CONFIG.policies.signUpSignIn),
    knownAuthorities: [`${AZURE_AD_B2C_CONFIG.tenantName}.b2clogin.com`],
    redirectUri: AZURE_AD_B2C_CONFIG.redirectUri,
    postLogoutRedirectUri: AZURE_AD_B2C_CONFIG.postLogoutRedirectUri,
    navigateToLoginRequestUrl: false,
  },
  cache: {
    cacheLocation: 'localStorage',
    storeAuthStateInCookie: false,
  },
  system: {
    loggerOptions: {
      loggerCallback: (level, message, containsPii) => {
        if (containsPii) return
        
        switch (level) {
          case LogLevel.Error:
            console.error('[MSAL]', message)
            break
          case LogLevel.Warning:
            console.warn('[MSAL]', message)
            break
          case LogLevel.Info:
            console.info('[MSAL]', message)
            break
          case LogLevel.Verbose:
            console.debug('[MSAL]', message)
            break
        }
      },
      logLevel: process.env.NODE_ENV === 'development' ? LogLevel.Verbose : LogLevel.Warning,
    },
  },
}

// Create MSAL instance
export const msalInstance = new PublicClientApplication(msalConfig)

// Request configurations for different policies
export const createLoginRequest = (usePopup = true): PopupRequest | RedirectRequest => ({
  scopes: AZURE_AD_B2C_CONFIG.scopes,
  authority: getAuthorityUrl(AZURE_AD_B2C_CONFIG.policies.signUpSignIn),
  prompt: 'select_account',
})

export const createPasswordResetRequest = (usePopup = true): PopupRequest | RedirectRequest => ({
  scopes: AZURE_AD_B2C_CONFIG.scopes,
  authority: getAuthorityUrl(AZURE_AD_B2C_CONFIG.policies.passwordReset),
})

export const createEditProfileRequest = (usePopup = true): PopupRequest | RedirectRequest => ({
  scopes: AZURE_AD_B2C_CONFIG.scopes,
  authority: getAuthorityUrl(AZURE_AD_B2C_CONFIG.policies.editProfile),
})

export const createSilentRequest = (account: AccountInfo): SilentRequest => ({
  scopes: AZURE_AD_B2C_CONFIG.scopes,
  account,
  authority: getAuthorityUrl(AZURE_AD_B2C_CONFIG.policies.signUpSignIn),
  forceRefresh: false,
})

/**
 * Enhanced Azure AD B2C Authentication Service
 * Supports all B2C policies with comprehensive error handling
 */
export class EnhancedAzureAdB2CAuthService {
  private initialized = false
  private currentPolicy: B2CPolicyType = B2CPolicyType.SIGN_UP_SIGN_IN

  /**
   * Initialize MSAL instance
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      await msalInstance.initialize()
      
      // Handle redirect promise and detect policy from response
      const response = await msalInstance.handleRedirectPromise()
      if (response) {
        console.log('Redirect response received:', response)
        this.detectPolicyFromResponse(response)
      }
      
      this.initialized = true
      console.log('Enhanced Azure AD B2C service initialized')
    } catch (error) {
      console.error('Failed to initialize MSAL:', error)
      throw new Error(`MSAL initialization failed: ${error}`)
    }
  }

  /**
   * Sign in with Azure AD B2C (supports both sign-in and sign-up)
   */
  async signIn(usePopup = true): Promise<StandardToken> {
    await this.initialize()
    this.currentPolicy = B2CPolicyType.SIGN_UP_SIGN_IN

    try {
      const request = createLoginRequest(usePopup)
      const response = usePopup 
        ? await msalInstance.loginPopup(request as PopupRequest)
        : await msalInstance.loginRedirect(request as RedirectRequest)

      if (!response && !usePopup) {
        // Redirect initiated, return placeholder token
        throw new Error('Redirect initiated')
      }

      if (!response?.accessToken) {
        throw new Error('No access token received from Azure AD B2C')
      }

      return this.convertToStandardToken(response)
    } catch (error: any) {
      return this.handleAuthError(error)
    }
  }

  /**
   * Sign up new user (redirects to sign-up flow)
   */
  async signUp(usePopup = true): Promise<StandardToken> {
    // For B2C, sign-up is handled by the same policy with different UI flow
    return this.signIn(usePopup)
  }

  /**
   * Reset password
   */
  async resetPassword(usePopup = true): Promise<void> {
    await this.initialize()
    this.currentPolicy = B2CPolicyType.PASSWORD_RESET

    try {
      const request = createPasswordResetRequest(usePopup)
      
      if (usePopup) {
        await msalInstance.loginPopup(request as PopupRequest)
      } else {
        await msalInstance.loginRedirect(request as RedirectRequest)
      }
      
      console.log('Password reset flow initiated')
    } catch (error: any) {
      // Handle cancellation gracefully
      if (error.errorCode === 'user_cancelled') {
        console.log('Password reset cancelled by user')
        return
      }
      
      console.error('Password reset failed:', error)
      throw new Error(`Password reset failed: ${error.message || 'Unknown error'}`)
    }
  }

  /**
   * Edit user profile
   */
  async editProfile(usePopup = true): Promise<StandardToken | void> {
    await this.initialize()
    this.currentPolicy = B2CPolicyType.EDIT_PROFILE

    try {
      const request = createEditProfileRequest(usePopup)
      
      const response = usePopup 
        ? await msalInstance.loginPopup(request as PopupRequest)
        : await msalInstance.loginRedirect(request as RedirectRequest)

      if (!response && !usePopup) {
        // Redirect initiated
        return
      }

      if (response?.accessToken) {
        return this.convertToStandardToken(response)
      }
      
      console.log('Profile edit completed')
    } catch (error: any) {
      // Handle cancellation gracefully
      if (error.errorCode === 'user_cancelled') {
        console.log('Profile edit cancelled by user')
        return
      }
      
      console.error('Profile edit failed:', error)
      throw new Error(`Profile edit failed: ${error.message || 'Unknown error'}`)
    }
  }

  /**
   * Sign out user
   */
  async signOut(usePopup = true): Promise<void> {
    await this.initialize()
    
    const accounts = msalInstance.getAllAccounts()
    if (accounts.length === 0) {
      console.log('No accounts to sign out')
      return
    }

    try {
      const logoutRequest: EndSessionRequest = {
        account: accounts[0],
        postLogoutRedirectUri: AZURE_AD_B2C_CONFIG.postLogoutRedirectUri,
      }

      if (usePopup) {
        await msalInstance.logoutPopup(logoutRequest)
      } else {
        await msalInstance.logoutRedirect(logoutRequest)
      }
      
      console.log('User signed out successfully')
    } catch (error) {
      console.error('Sign out failed:', error)
      throw new Error(`Sign out failed: ${error}`)
    }
  }

  /**
   * Get access token silently
   */
  async getAccessToken(): Promise<string | null> {
    await this.initialize()

    const accounts = msalInstance.getAllAccounts()
    if (accounts.length === 0) {
      return null
    }

    try {
      const request = createSilentRequest(accounts[0])
      const response = await msalInstance.acquireTokenSilent(request)
      return response.accessToken
    } catch (error) {
      console.warn('Silent token acquisition failed:', error)
      
      try {
        // Fallback to popup
        const request = createLoginRequest(true) as PopupRequest
        const response = await msalInstance.acquireTokenPopup(request)
        return response.accessToken
      } catch (popupError) {
        console.error('Token acquisition failed:', popupError)
        return null
      }
    }
  }

  /**
   * Get current user information
   */
  async getCurrentUser(): Promise<UserContext | null> {
    await this.initialize()

    const accounts = msalInstance.getAllAccounts()
    if (accounts.length === 0) {
      return null
    }

    const account = accounts[0]
    const claims = account.idTokenClaims as any

    return {
      id: account.localAccountId,
      email: account.username,
      displayName: claims?.name || claims?.given_name || account.name || '',
      firstName: claims?.given_name || '',
      lastName: claims?.family_name || '',
      roles: this.extractRoles(claims),
      organizationIds: this.extractOrganizations(claims),
      tenantId: account.tenantId || '',
      permissions: this.extractPermissions(claims),
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    if (!this.initialized) return false
    
    const accounts = msalInstance.getAllAccounts()
    return accounts.length > 0
  }

  /**
   * Get all accounts
   */
  getAllAccounts(): AccountInfo[] {
    if (!this.initialized) return []
    return msalInstance.getAllAccounts()
  }

  /**
   * Convert MSAL response to StandardToken
   */
  private convertToStandardToken(response: AuthenticationResult): StandardToken {
    const claims = response.idTokenClaims as any
    
    return {
      accessToken: response.accessToken,
      refreshToken: response.account?.idToken || undefined,
      expiresAt: response.expiresOn ? response.expiresOn.getTime() : Date.now() + 3600000,
      userId: response.account?.localAccountId || '',
      email: response.account?.username || '',
      roles: this.extractRoles(claims),
      organizationIds: this.extractOrganizations(claims),
      tenantId: response.account?.tenantId || '',
    }
  }

  /**
   * Handle authentication errors with policy-specific logic
   */
  private async handleAuthError(error: any): Promise<StandardToken> {
    console.error('Authentication error:', error)

    // Handle password reset flow
    if (error.errorMessage?.includes('AADB2C90118') || error.errorCode === 'forgot_password') {
      console.log('Password reset required, initiating reset flow')
      await this.resetPassword()
      throw new Error('Password reset initiated. Please complete the reset process and try signing in again.')
    }

    // Handle user cancellation
    if (error.errorCode === 'user_cancelled') {
      throw new Error('Sign in was cancelled by the user.')
    }

    // Handle network errors
    if (error.errorCode === 'network_error') {
      throw new Error('Network error occurred. Please check your connection and try again.')
    }

    // Handle invalid grant (expired/invalid tokens)
    if (error.errorCode === 'invalid_grant') {
      throw new Error('Session expired. Please sign in again.')
    }

    // Generic error handling
    throw new Error(`Authentication failed: ${error.message || error.errorMessage || 'Unknown error'}`)
  }

  /**
   * Detect policy from authentication response
   */
  private detectPolicyFromResponse(response: AuthenticationResult): void {
    const authority = response.authority || ''
    
    if (authority.includes(AZURE_AD_B2C_CONFIG.policies.passwordReset)) {
      this.currentPolicy = B2CPolicyType.PASSWORD_RESET
    } else if (authority.includes(AZURE_AD_B2C_CONFIG.policies.editProfile)) {
      this.currentPolicy = B2CPolicyType.EDIT_PROFILE
    } else {
      this.currentPolicy = B2CPolicyType.SIGN_UP_SIGN_IN
    }
    
    console.log('Detected policy:', this.currentPolicy)
  }

  /**
   * Extract roles from token claims
   */
  private extractRoles(claims: any): string[] {
    if (!claims) return []
    
    const roles = claims.roles || 
                 claims.extension_roles || 
                 claims['extension_Roles'] || 
                 claims.jobTitle || 
                 []
    
    return Array.isArray(roles) ? roles : [roles].filter(Boolean)
  }

  /**
   * Extract organization IDs from token claims
   */
  private extractOrganizations(claims: any): string[] {
    if (!claims) return []
    
    const orgs = claims.organizations || 
                claims.extension_organizations || 
                claims['extension_Organizations'] || 
                []
    
    return Array.isArray(orgs) ? orgs : [orgs].filter(Boolean)
  }

  /**
   * Extract permissions from token claims
   */
  private extractPermissions(claims: any): string[] {
    if (!claims) return []
    
    const permissions = claims.permissions || 
                       claims.extension_permissions || 
                       claims['extension_Permissions'] || 
                       []
    
    return Array.isArray(permissions) ? permissions : [permissions].filter(Boolean)
  }

  /**
   * Get current policy type
   */
  getCurrentPolicy(): B2CPolicyType {
    return this.currentPolicy
  }
}

// Export singleton instance
export const enhancedAzureAdB2CAuthService = new EnhancedAzureAdB2CAuthService()

// Export for backward compatibility
export default enhancedAzureAdB2CAuthService
