'use client'

/**
 * Production Login Page
 * Clean, intuitive Azure AD B2C authentication with popup flow
 */

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  AlertCircle,
  LogIn,
  UserPlus,
  Loader2,
  ArrowRight,
  CheckCircle2
} from 'lucide-react'
import { useEnhancedAuth, AuthMethod } from '@/hooks/useEnhancedAuth'

export default function LoginPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const returnUrl = searchParams.get('returnUrl') || '/dashboard'
  const mode = searchParams.get('mode') || 'signin' // 'signin' or 'signup'

  const { isAuthenticated, isLoading, user, error, clearError, signIn, signUp } = useEnhancedAuth()
  const [showSuccess, setShowSuccess] = useState(false)

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      setShowSuccess(true)
      setTimeout(() => {
        router.push(returnUrl)
      }, 1500)
    }
  }, [isAuthenticated, user, router, returnUrl])

  // Handle sign in
  const handleSignIn = async () => {
    try {
      clearError()
      await signIn(AuthMethod.POPUP)
    } catch (error) {
      console.error('Sign in failed:', error)
    }
  }

  // Handle sign up
  const handleSignUp = async () => {
    try {
      clearError()
      await signUp(AuthMethod.POPUP)
    } catch (error) {
      console.error('Sign up failed:', error)
    }
  }



  // Show success state
  if (showSuccess && isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <CheckCircle2 className="h-16 w-16 text-green-500 mx-auto" />
              <div>
                <h2 className="text-2xl font-bold text-green-700">Welcome!</h2>
                <p className="text-muted-foreground">
                  Successfully authenticated as {user?.email}
                </p>
              </div>
              <div className="text-sm text-muted-foreground">
                Redirecting to dashboard...
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4 py-12">
      <div className="w-full max-w-md space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">Welcome to HEPZ</h1>
          <p className="mt-2 text-gray-600">Sign in to your account to continue</p>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Main Login Card */}
        <Card className="shadow-lg">
          <CardHeader className="space-y-1 pb-4">
            <CardTitle className="text-xl text-center">
              {mode === 'signup' ? 'Create Account' : 'Sign In'}
            </CardTitle>
            <CardDescription className="text-center">
              {mode === 'signup'
                ? 'Get started with your new account'
                : 'Access your documents and workflows'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Sign In Button */}
            {mode === 'signin' && (
              <Button
                onClick={handleSignIn}
                disabled={isLoading}
                className="w-full h-11 text-base"
                size="lg"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  <>
                    <LogIn className="mr-2 h-4 w-4" />
                    Sign in with Microsoft
                  </>
                )}
              </Button>
            )}

            {/* Sign Up Button */}
            {mode === 'signup' && (
              <Button
                onClick={handleSignUp}
                disabled={isLoading}
                className="w-full h-11 text-base"
                size="lg"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating account...
                  </>
                ) : (
                  <>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Create account with Microsoft
                  </>
                )}
              </Button>
            )}


            {/* Mode Switch */}
            <div className="text-center pt-4 border-t">
              <p className="text-sm text-gray-600">
                {mode === 'signin' ? "Don't have an account?" : "Already have an account?"}
              </p>
              <Button
                variant="link"
                className="p-0 h-auto text-blue-600 hover:text-blue-700"
                onClick={() => {
                  const newMode = mode === 'signin' ? 'signup' : 'signin'
                  const newUrl = new URL(window.location.href)
                  newUrl.searchParams.set('mode', newMode)
                  router.push(newUrl.toString())
                }}
              >
                {mode === 'signin' ? 'Create an account' : 'Sign in instead'}
                <ArrowRight className="ml-1 h-3 w-3" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          <p>Secure authentication powered by Microsoft</p>
        </div>
      </div>
    </div>
  )
}
