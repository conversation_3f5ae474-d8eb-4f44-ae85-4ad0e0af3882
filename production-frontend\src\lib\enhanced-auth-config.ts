/**
 * Enhanced Authentication Configuration
 * NextAuth.js configuration for Azure AD B2C with all policies support
 */

import { NextAuthOptions } from 'next-auth'
import AzureADB2CProvider from 'next-auth/providers/azure-ad-b2c'
import CredentialsProvider from 'next-auth/providers/credentials'
import { backendApiClient } from '@/services/backend-api-client'

// Azure AD B2C Configuration
const AZURE_AD_B2C_CONFIG = {
  tenantName: process.env.AZURE_AD_B2C_TENANT_NAME || 'hepzdocs',
  clientId: process.env.AZURE_AD_B2C_CLIENT_ID || '',
  clientSecret: process.env.AZURE_AD_B2C_CLIENT_SECRET || '',
  domain: 'hepzdocs.b2clogin.com',
  policies: {
    signIn: 'B2C_1_SI',
    signUp: 'B2C_1_SU',
    signUpSignIn: 'B2C_1_SUSI',
    passwordReset: 'B2C_1_passwordreset1',
    editProfile: 'B2C_1_profileedit1'
  },
  endpoints: {
    authorization: 'https://hepzdocs.b2clogin.com/hepzdocs.onmicrosoft.com/{policy}/oauth2/v2.0/authorize',
    token: 'https://hepzdocs.b2clogin.com/hepzdocs.onmicrosoft.com/{policy}/oauth2/v2.0/token',
    logout: 'https://hepzdocs.b2clogin.com/hepzdocs.onmicrosoft.com/{policy}/oauth2/v2.0/logout',
    metadata: 'https://hepzdocs.b2clogin.com/hepzdocs.onmicrosoft.com/{policy}/v2.0/.well-known/openid-configuration'
  }
}

/**
 * Refresh the access token using the refresh token
 */
async function refreshAccessToken(token: any) {
  try {
    if (!token.refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await backendApiClient.auth.refreshToken(token.refreshToken)

    return {
      ...token,
      accessToken: response.accessToken,
      refreshToken: response.refreshToken ?? token.refreshToken,
      expiresAt: Date.now() + 60 * 60 * 1000, // 1 hour
    }
  } catch (error) {
    console.error('Error refreshing access token:', error)

    return {
      ...token,
      error: 'RefreshAccessTokenError',
    }
  }
}

export const enhancedAuthOptions: NextAuthOptions = {
  providers: [
    // Azure AD B2C Provider for Sign In
    AzureADB2CProvider({
      id: 'azure-ad-b2c-signin',
      name: 'Microsoft Account',
      tenantId: AZURE_AD_B2C_CONFIG.tenantName,
      clientId: AZURE_AD_B2C_CONFIG.clientId,
      clientSecret: AZURE_AD_B2C_CONFIG.clientSecret,
      primaryUserFlow: AZURE_AD_B2C_CONFIG.policies.signUpSignIn,
      authorization: {
        url: AZURE_AD_B2C_CONFIG.endpoints.authorization.replace('{policy}', AZURE_AD_B2C_CONFIG.policies.signUpSignIn),
        params: {
          scope: 'openid profile email offline_access',
          prompt: 'select_account',
          response_type: 'code',
          response_mode: 'query'
        },
      },
      token: AZURE_AD_B2C_CONFIG.endpoints.token.replace('{policy}', AZURE_AD_B2C_CONFIG.policies.signUpSignIn),
      userinfo: AZURE_AD_B2C_CONFIG.endpoints.metadata.replace('{policy}', AZURE_AD_B2C_CONFIG.policies.signUpSignIn),
      profile: (profile) => {
        return {
          id: profile.sub || profile.oid,
          name: profile.name || profile.given_name,
          email: profile.email || profile.emails?.[0],
          image: profile.picture,
          // Extract custom claims
          roles: profile.extension_roles || profile.roles || [],
          organizationIds: profile.extension_organizations || profile.organizations || [],
          tenantId: profile.tid,
          permissions: profile.extension_permissions || profile.permissions || [],
        }
      },
    }),

    // Credentials Provider for fallback authentication
    CredentialsProvider({
      id: 'credentials',
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          const response = await backendApiClient.auth.login({
            email: credentials.email,
            password: credentials.password
          })

          if (response.user && response.token) {
            return {
              id: response.user.id,
              email: response.user.email,
              name: response.user.name,
              accessToken: response.token.accessToken,
              roles: response.user.roles || [],
              organizationIds: response.user.organizationIds || [],
            }
          }
          return null
        } catch (error) {
          console.error('Credentials authentication failed:', error)
          return null
        }
      }
    })
  ],

  // Enhanced callbacks for B2C policy handling
  callbacks: {
    async jwt({ token, user, account }) {
      // Initial sign in
      if (account && user) {
        token.accessToken = account.access_token
        token.refreshToken = account.refresh_token
        token.expiresAt = account.expires_at
        token.provider = account.provider
        token.roles = (user as any).roles || []
        token.organizationIds = (user as any).organizationIds || []
        token.permissions = (user as any).permissions || []
        token.tenantId = (user as any).tenantId || ''

        // Handle different B2C policies
        if (account.provider === 'azure-ad-b2c') {
          const authority = account.id_token ? 
            JSON.parse(Buffer.from(account.id_token.split('.')[1], 'base64').toString()).iss : ''
          
          if (authority.includes(AZURE_AD_B2C_CONFIG.policies.passwordReset)) {
            token.policy = 'password_reset'
          } else if (authority.includes(AZURE_AD_B2C_CONFIG.policies.editProfile)) {
            token.policy = 'edit_profile'
          } else {
            token.policy = 'sign_up_sign_in'
          }
        }
      }

      // Return previous token if the access token has not expired yet
      if (Date.now() < (token.expiresAt as number) * 1000) {
        return token
      }

      // Access token has expired, try to update it
      return await refreshAccessToken(token)
    },

    async session({ session, token }) {
      // Send properties to the client
      ;(session as any).accessToken = token.accessToken as string
      ;(session as any).error = token.error as string
      ;(session as any).policy = token.policy as string
      
      if (session.user) {
        ;(session.user as any).id = token.sub as string
        ;(session.user as any).roles = token.roles as string[]
        ;(session.user as any).organizationIds = token.organizationIds as string[]
        ;(session.user as any).permissions = token.permissions as string[]
        ;(session.user as any).tenantId = token.tenantId as string
      }

      return session
    },

    async signIn() {
      // Allow sign in for all providers
      return true
    },

    async redirect({ url, baseUrl }) {
      // Handle B2C policy redirects
      const urlObj = new URL(url, baseUrl)
      
      // Check for B2C error codes
      if (urlObj.searchParams.has('error')) {
        const error = urlObj.searchParams.get('error')
        const errorDescription = urlObj.searchParams.get('error_description')
        
        // Handle password reset flow
        if (error === 'access_denied' && errorDescription?.includes('AADB2C90118')) {
          return `${baseUrl}/auth/password-reset`
        }
        
        // Handle other errors
        return `${baseUrl}/auth/error?error=${error}`
      }
      
      // Allows relative callback URLs
      if (url.startsWith('/')) return `${baseUrl}${url}`
      
      // Allows callback URLs on the same origin
      if (new URL(url).origin === baseUrl) return url
      
      return baseUrl
    }
  },

  // Enhanced pages configuration
  pages: {
    signIn: '/auth/enhanced-login',
    signOut: '/auth/signout',
    error: '/auth/error',
    verifyRequest: '/auth/verify-request',
    newUser: '/auth/new-user'
  },

  // Session configuration
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },

  // JWT configuration
  jwt: {
    maxAge: 60 * 60 * 24 * 30, // 30 days
  },

  // Enhanced events
  events: {
    async signIn({ user, account, isNewUser }) {
      console.log('User signed in:', { 
        userId: user.id, 
        email: user.email, 
        provider: account?.provider,
        isNewUser 
      })
    },
    async signOut({ token }) {
      console.log('User signed out:', { userId: token?.sub })
    },
    async createUser({ user }) {
      console.log('New user created:', { userId: user.id, email: user.email })
    },
    async updateUser({ user }) {
      console.log('User updated:', { userId: user.id, email: user.email })
    },
    async linkAccount({ user, account }) {
      console.log('Account linked:', { 
        userId: user.id, 
        provider: account.provider 
      })
    },
    async session() {
      // Session accessed - could be used for analytics
    }
  },

  // Debug configuration
  debug: process.env.NODE_ENV === 'development',
}

/**
 * Get the current session server-side
 */
export async function getServerSession() {
  const { getServerSession } = await import('next-auth/next')
  return getServerSession(enhancedAuthOptions)
}

/**
 * Auth configuration for middleware
 */
export const authConfig = {
  pages: {
    signIn: '/auth/enhanced-login',
  },
  callbacks: {
    // @ts-ignore - NextAuth types issue
    authorized({ auth, request: { nextUrl } }: any) {
      const isLoggedIn = !!auth?.user
      const isOnDashboard = nextUrl.pathname.startsWith('/app')
      const isOnAuth = nextUrl.pathname.startsWith('/auth')

      if (isOnDashboard) {
        if (isLoggedIn) return true
        return false // Redirect unauthenticated users to login page
      } else if (isLoggedIn && isOnAuth) {
        return Response.redirect(new URL('/app/dashboard', nextUrl))
      }

      return true
    },
  },
  providers: [], // Add providers with an empty array for Edge compatibility
} satisfies NextAuthOptions

// Export for backward compatibility
export const authOptions = enhancedAuthOptions
export default enhancedAuthOptions
