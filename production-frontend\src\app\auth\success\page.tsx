'use client'

/**
 * Authentication Success Page
 * Handles successful authentication and redirects to dashboard
 */

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { CheckCircle2, Loader2 } from 'lucide-react'
import { useAuthStore } from '@/stores/auth-store'
import { useNotificationStore } from '@/stores/notification-store'

export default function AuthSuccessPage() {
  const router = useRouter()
  const [isProcessing, setIsProcessing] = useState(true)
  const { handleB2CAuthResult } = useAuthStore()
  const { addNotification } = useNotificationStore()

  useEffect(() => {
    const processAuthentication = async () => {
      try {
        // Get user info from cookie
        const userInfoCookie = document.cookie
          .split('; ')
          .find(row => row.startsWith('msal_user_info='))
          ?.split('=')[1]

        const sessionCookie = document.cookie
          .split('; ')
          .find(row => row.startsWith('msal_session='))
          ?.split('=')[1]

        if (userInfoCookie && sessionCookie) {
          const userInfo = JSON.parse(decodeURIComponent(userInfoCookie))
          const sessionInfo = JSON.parse(decodeURIComponent(sessionCookie))

          // Handle B2C authentication result
          handleB2CAuthResult(userInfo, sessionInfo)

          // Show success notification
          addNotification({
            type: 'success',
            title: 'Welcome!',
            message: `Successfully signed in as ${userInfo.username}`
          })

          // Redirect to dashboard after a short delay
          setTimeout(() => {
            router.push('/dashboard')
          }, 2000)
        } else {
          throw new Error('No authentication data found')
        }

      } catch (error) {
        console.error('[Auth Success] Processing failed:', error)
        
        addNotification({
          type: 'error',
          title: 'Authentication Error',
          message: 'Failed to process authentication. Please try again.'
        })

        // Redirect back to login
        setTimeout(() => {
          router.push('/auth/enhanced-login')
        }, 3000)
      } finally {
        setIsProcessing(false)
      }
    }

    processAuthentication()
  }, [router, handleB2CAuthResult, addNotification])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4 py-12">
      <div className="w-full max-w-md space-y-8">
        <Card className="shadow-lg">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              {isProcessing ? (
                <>
                  <Loader2 className="h-16 w-16 animate-spin text-blue-600 mx-auto" />
                  <h2 className="text-xl font-semibold text-gray-900">Processing Authentication</h2>
                  <p className="text-gray-600">Setting up your session...</p>
                </>
              ) : (
                <>
                  <CheckCircle2 className="h-16 w-16 text-green-500 mx-auto" />
                  <h2 className="text-xl font-semibold text-gray-900">Authentication Successful!</h2>
                  <p className="text-gray-600">Redirecting to dashboard...</p>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
