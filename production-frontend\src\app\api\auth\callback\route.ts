/**
 * Server-side Azure AD B2C Token Exchange API
 * Exchanges authorization code for tokens using client secret
 */

import { NextRequest, NextResponse } from 'next/server'
import { ConfidentialClientApplication } from '@azure/msal-node'
import { cookies } from 'next/headers'
import { createMSALConfig, B2C_POLICIES, B2C_SCOPES, getRedirectUri, getPostLogoutRedirectUri } from '@/lib/auth/msal-node-config'

// Helper function to extract policy from state parameter
const extractPolicyFromState = (state: string | null): string => {
  if (!state) {
    console.warn('[Auth Callback] No state parameter found, using default policy')
    return B2C_POLICIES.SIGN_UP_SIGN_IN
  }

  try {
    const stateData = JSON.parse(state)
    if (stateData.policy) {
      console.log('[Auth Callback] Extracted policy from state:', stateData.policy)
      return stateData.policy
    }
  } catch (error) {
    console.warn('[Auth Callback] Could not parse state parameter:', error)
  }

  console.warn('[Auth Callback] No policy found in state, using default policy')
  return B2C_POLICIES.SIGN_UP_SIGN_IN
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')
    const state = searchParams.get('state')
    const error = searchParams.get('error')
    const errorDescription = searchParams.get('error_description')

    // Handle authentication errors
    if (error) {
      console.error('[Auth Callback] Authentication error:', error, errorDescription)
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_AZURE_AD_B2C_POST_LOGOUT_REDIRECT_URI}/auth/enhanced-login?error=${encodeURIComponent(error)}&error_description=${encodeURIComponent(errorDescription || '')}`
      )
    }

    // Validate authorization code
    if (!code) {
      console.error('[Auth Callback] No authorization code received')
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_AZURE_AD_B2C_POST_LOGOUT_REDIRECT_URI}/auth/enhanced-login?error=no_code`
      )
    }

    // Extract policy from state parameter to ensure token exchange uses correct policy
    const policy = extractPolicyFromState(state)
    console.log('[Auth Callback] Using policy for token exchange:', policy)

    // Create MSAL instance with the correct policy for token exchange
    const msalConfig = createMSALConfig(policy)
    const msalInstance = new ConfidentialClientApplication(msalConfig)

    // Exchange authorization code for tokens
    const tokenRequest = {
      code: code,
      scopes: B2C_SCOPES,
      redirectUri: getRedirectUri(),
    }

    console.log('[Auth Callback] Exchanging code for tokens...')
    console.log('[Auth Callback] Token request:', { ...tokenRequest, code: 'REDACTED' })
    const response = await msalInstance.acquireTokenByCode(tokenRequest)

    if (!response) {
      throw new Error('No token response received')
    }

    console.log('[Auth Callback] Token exchange successful for user:', response.account?.username)

    // Create secure session data
    const sessionData = {
      accessToken: response.accessToken,
      idToken: response.idToken,
      account: {
        homeAccountId: response.account?.homeAccountId,
        environment: response.account?.environment,
        tenantId: response.account?.tenantId,
        username: response.account?.username,
        localAccountId: response.account?.localAccountId,
        name: response.account?.name,
      },
      idTokenClaims: response.idTokenClaims,
      expiresOn: response.expiresOn?.getTime(),
      scopes: response.scopes,
    }

    // Set secure HTTP-only cookies
    const cookieStore = await cookies()

    // Set access token cookie (HTTP-only for security)
    cookieStore.set('msal_access_token', response.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 3600, // 1 hour
      path: '/',
    })

    // Set user info cookie (can be read by client)
    cookieStore.set('msal_user_info', JSON.stringify({
      username: response.account?.username,
      name: response.account?.name,
      localAccountId: response.account?.localAccountId,
      tenantId: response.account?.tenantId,
    }), {
      httpOnly: false, // Allow client-side access
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 3600, // 1 hour
      path: '/',
    })

    // Set session cookie
    cookieStore.set('msal_session', JSON.stringify({
      isAuthenticated: true,
      expiresOn: response.expiresOn?.getTime(),
      scopes: response.scopes,
    }), {
      httpOnly: false, // Allow client-side access
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 3600, // 1 hour
      path: '/',
    })

    // Redirect to success page
    return NextResponse.redirect(
      `${getPostLogoutRedirectUri()}/auth/success`
    )

  } catch (error) {
    console.error('[Auth Callback] Token exchange failed:', error)
    
    return NextResponse.redirect(
      `${getPostLogoutRedirectUri()}/auth/enhanced-login?error=token_exchange_failed&error_description=${encodeURIComponent(error instanceof Error ? error.message : 'Unknown error')}`
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { code, state } = await request.json()

    if (!code) {
      throw new Error('Authorization code is required')
    }

    // Extract policy from state parameter
    const policy = extractPolicyFromState(state)
    console.log('[Auth Callback API] Using policy for token exchange:', policy)

    // Create MSAL instance with the correct policy for token exchange
    const msalConfig = createMSALConfig(policy)
    const msalInstance = new ConfidentialClientApplication(msalConfig)

    // Exchange authorization code for tokens
    const tokenRequest = {
      code: code,
      scopes: B2C_SCOPES,
      redirectUri: getRedirectUri(),
    }

    const response = await msalInstance.acquireTokenByCode(tokenRequest)

    if (!response) {
      throw new Error('No token response received')
    }

    // Return token data (for API usage)
    return NextResponse.json({
      success: true,
      accessToken: response.accessToken,
      idToken: response.idToken,
      account: response.account,
      idTokenClaims: response.idTokenClaims,
      expiresOn: response.expiresOn?.getTime(),
      scopes: response.scopes,
    })

  } catch (error) {
    console.error('[Auth Callback API] Token exchange failed:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Token exchange failed',
      },
      { status: 500 }
    )
  }
}
