/**
 * Server-side Azure AD B2C Token Exchange API
 * Exchanges authorization code for tokens using client secret
 */

import { NextRequest, NextResponse } from 'next/server'
import { ConfidentialClientApplication, Configuration } from '@azure/msal-node'
import { cookies } from 'next/headers'

// Server-side MSAL configuration with client secret
const msalConfig: Configuration = {
  auth: {
    clientId: process.env.AZURE_AD_B2C_CLIENT_ID!,
    clientSecret: process.env.AZURE_AD_B2C_CLIENT_SECRET!,
    authority: `https://${process.env.AZURE_AD_B2C_AUTHORITY_DOMAIN}/${process.env.AZURE_AD_B2C_TENANT_NAME}.onmicrosoft.com/${process.env.NEXT_PUBLIC_AZURE_AD_B2C_SUSI_POLICY}`,
  },
  system: {
    loggerOptions: {
      loggerCallback: (level, message, containsPii) => {
        if (containsPii) return
        console.log(`[MSAL Server] ${message}`)
      },
      piiLoggingEnabled: false,
      logLevel: 3, // Info level
    },
  },
}

// Create MSAL instance
const msalInstance = new ConfidentialClientApplication(msalConfig)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')
    const state = searchParams.get('state')
    const error = searchParams.get('error')
    const errorDescription = searchParams.get('error_description')

    // Handle authentication errors
    if (error) {
      console.error('[Auth Callback] Authentication error:', error, errorDescription)
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_AZURE_AD_B2C_POST_LOGOUT_REDIRECT_URI}/auth/enhanced-login?error=${encodeURIComponent(error)}&error_description=${encodeURIComponent(errorDescription || '')}`
      )
    }

    // Validate authorization code
    if (!code) {
      console.error('[Auth Callback] No authorization code received')
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_AZURE_AD_B2C_POST_LOGOUT_REDIRECT_URI}/auth/enhanced-login?error=no_code`
      )
    }

    // Exchange authorization code for tokens
    const tokenRequest = {
      code: code,
      scopes: ['openid', 'profile', 'email'],
      redirectUri: process.env.NEXT_PUBLIC_AZURE_AD_B2C_REDIRECT_URI!,
    }

    console.log('[Auth Callback] Exchanging code for tokens...')
    const response = await msalInstance.acquireTokenByCode(tokenRequest)

    if (!response) {
      throw new Error('No token response received')
    }

    console.log('[Auth Callback] Token exchange successful for user:', response.account?.username)

    // Create secure session data
    const sessionData = {
      accessToken: response.accessToken,
      idToken: response.idToken,
      account: {
        homeAccountId: response.account?.homeAccountId,
        environment: response.account?.environment,
        tenantId: response.account?.tenantId,
        username: response.account?.username,
        localAccountId: response.account?.localAccountId,
        name: response.account?.name,
      },
      idTokenClaims: response.idTokenClaims,
      expiresOn: response.expiresOn?.getTime(),
      scopes: response.scopes,
    }

    // Set secure HTTP-only cookies
    const cookieStore = cookies()
    
    // Set access token cookie (HTTP-only for security)
    cookieStore.set('msal_access_token', response.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 3600, // 1 hour
      path: '/',
    })

    // Set user info cookie (can be read by client)
    cookieStore.set('msal_user_info', JSON.stringify({
      username: response.account?.username,
      name: response.account?.name,
      localAccountId: response.account?.localAccountId,
      tenantId: response.account?.tenantId,
    }), {
      httpOnly: false, // Allow client-side access
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 3600, // 1 hour
      path: '/',
    })

    // Set session cookie
    cookieStore.set('msal_session', JSON.stringify({
      isAuthenticated: true,
      expiresOn: response.expiresOn?.getTime(),
      scopes: response.scopes,
    }), {
      httpOnly: false, // Allow client-side access
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 3600, // 1 hour
      path: '/',
    })

    // Redirect to success page
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_AZURE_AD_B2C_POST_LOGOUT_REDIRECT_URI}/auth/success`
    )

  } catch (error) {
    console.error('[Auth Callback] Token exchange failed:', error)
    
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_AZURE_AD_B2C_POST_LOGOUT_REDIRECT_URI}/auth/enhanced-login?error=token_exchange_failed&error_description=${encodeURIComponent(error instanceof Error ? error.message : 'Unknown error')}`
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { code, state } = await request.json()

    if (!code) {
      throw new Error('Authorization code is required')
    }

    // Exchange authorization code for tokens
    const tokenRequest = {
      code: code,
      scopes: ['openid', 'profile', 'email'],
      redirectUri: process.env.NEXT_PUBLIC_AZURE_AD_B2C_REDIRECT_URI!,
    }

    const response = await msalInstance.acquireTokenByCode(tokenRequest)

    if (!response) {
      throw new Error('No token response received')
    }

    // Return token data (for API usage)
    return NextResponse.json({
      success: true,
      accessToken: response.accessToken,
      idToken: response.idToken,
      account: response.account,
      idTokenClaims: response.idTokenClaims,
      expiresOn: response.expiresOn?.getTime(),
      scopes: response.scopes,
    })

  } catch (error) {
    console.error('[Auth Callback API] Token exchange failed:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Token exchange failed',
      },
      { status: 500 }
    )
  }
}
